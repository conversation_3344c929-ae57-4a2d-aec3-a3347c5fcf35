<resources>
    <string name="power_station_report">Power Station Report</string>
    <string name="app_name">Smart Photovoltaic</string>
    <string name="app_name_ymx">Ymx Smart Photovoltaic</string>
    <string name="app_name_lj">LongJi Smart Photovoltaic</string>

    <!-- User Agreement Related -->
    <string name="user_agreement">%sService Term</string>
    <string name="privacy_policy">%sPrivacy</string>
    <string name="i_have_read">I have read</string>
    <string name="and">%sand</string>
    <string name="forgot_password"> Forgot%s</string>

    <string name="tab_home">index</string>
    <string name="tab_operation">Operation</string>
    <string name="tab_equipment">Equipment</string>
    <string name="tab_mine">Mine</string>
    <string name="tab_edit">Edit</string>
    <string name="info_edit">Information Edit</string>
    <string name="language_setting">Language Settings</string>

    <string name="tab_view">View</string>
    <string name="tab_report">Report</string>

    <string name="toast_double_back_exit">Press again to exit the app</string>
    <string name="system_settings">System Settings</string>
    <string name="login">Login Now</string>
    <string name="register">Register Now</string>
    <string name="welcome_hello">Hello!</string>
    <string name="welcome_login">Welcome</string>
    <string name="welcome_register">Hello!\nWelcome to Register</string>
    <string name="account">Account</string>
    <string name="password">Password</string>
    <string name="password_sure">Confirm Password</string>
    <string name="user_name">Username</string>
    <string name="input_user_name">Please enter your account/phone</string>
    <string name="input_phone_or_email">Please enter phone or email</string>
    <string name="input_login_code">Please enter the registration code</string>
    <string name="input_password">Please enter your password</string>
    <string name="input_power_name">Please enter the power station name</string>
    <string name="input_query_content">Please enter the query content</string>

    <string name="password_login">Password Login</string>
    <string name="register_account">Register Account</string>
    <string name="have_account">Already have an account</string>
    <string name="no_account">No account?</string>
    <string name="to_login">Go to Login →</string>
    <string name="to_register">Register →</string>
    <string name="scan_tip">Please scan the QR code of the device</string>
    <string name="collector_management">Collector Management</string>
    <string name="edit_collector">Edit Collector</string>
    <string name="add_collector">Add Collector</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="equipment_management">Equipment Management</string>
    <string name="collector_info">Collector Info</string>
    <string name="save">Save</string>
    <string name="group_management">Group Management</string>
    <string name="edit_group">Edit Group</string>
    <string name="optimizer_management">Optimizer Management</string>
    <string name="edit_optimizer">Edit Optimizer</string>
    <string name="optimizer_info">Optimizer Info</string>
    <string name="optimizer_id">Optimizer ID</string>
    <string name="model">Model</string>
    <string name="add_station">Add Station</string>
    <string name="settings">Settings</string>
    <string name="messages">Operation</string>
    <string name="operation_info">Operation Information</string>
    <string name="message_detail">Message Detail</string>
    <string name="feedback">Feedback</string>
    <string name="change_password">Change Password</string>
    <string name="logout">Logout</string>
    <string name="user_role">User</string>
    <string name="set_wifi">Set WiFi</string>
    <string name="group_info">Group Info</string>
    <string name="group_name">Group Name</string>
    <string name="contact_info">Contact Info</string>
    <string name="submit">Submit</string>
    <string name="input_feedback">Please enter your feedback</string>
    <string name="feedback_hint">Please describe the issue in detail...</string>
    <string name="old_password">Old Password</string>
    <string name="new_password">New Password</string>
    <string name="confirm_new_password">Confirm New Password</string>
    <string name="confirm_change">Confirm Change</string>
    <string name="wifi_name">WiFi Name</string>
    <string name="wifi_password">WiFi Password</string>
    <string name="sending">Sending...</string>
    <string name="network_error">Network Error</string>
    <string name="network_error_hint">Network disconnected, try refreshing</string>
    <string name="refresh">Refresh</string>
    <string name="input_confirm_password">Please confirm password</string>
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="select_time">Select Time</string>
    <string name="hour">h</string>
    <string name="minute">m</string>
    <string name="second">s</string>
    <string name="input_old_password">Please enter original password</string>
    <string name="input_new_password">Please enter new password</string>
    <string name="input_confirm_new_password">Please confirm new password</string>
    <string name="password_not_match">Passwords do not match</string>
    <string name="please_input_password">Please enter password</string>

    <string name="optimizer_count">Optimizer Count:</string>

    <!-- GroupSinglePage -->
    <string name="series">Series</string>
    <string name="parallel">Parallel</string>

    <!-- StationSinglePage & StationInfoPage -->
    <string name="system_name">Station Name</string>
    <string name="area">Area</string>

    <!-- CollectorSinglePage -->
    <string name="collector_name">Collector Name</string>
    <string name="imei">IMEI</string>

    <!-- HomePage & StationViewPage -->
    <string name="temp_warning">Temperature Warning</string>
    <string name="temp_warning_set">Temperature Warning Setting</string>

    <string name="temp_shutdown">High Temperature Shutdown Setting</string>
    <string name="shade_warning">Shade Warning</string>
    <string name="daily_power">Daily Gen</string>
    <string name="monthly_power">Monthly Gen</string>
    <string name="yearly_power">Yearly Gen</string>

    <!-- LoginPage -->
    <string name="logging_in">Logging in...</string>
    <string name="login_failed">Login failed, incorrect username or password!</string>

    <!-- ForgetPasswordPage -->
    <string name="get_verification_code">Get Verification Code</string>


    <!-- New Additions -->
    <string name="days_ago">days ago</string>
    <string name="today">Today</string>
    <string name="yesterday">Yesterday</string>
    <string name="status_all">All</string>
    <string name="status_normal">Normal</string>
    <string name="status_abnormal">Abnormal</string>
    <string name="status_offline">Offline</string>
    <string name="warning_title">Warning</string>
    <string name="warning_message">Are you sure you want to delete?</string>
    <string name="warning_count">Warning Count: </string>
    <string name="create_time">Create Time: </string>
    <string name="search">Search</string>
    <string name="please_input_wifi_name">Please enter WiFi name</string>
    <string name="please_input_wifi_password">Please enter WiFi password</string>
    <string name="send_success">Send Successfully</string>
    <string name="send_failed">Send Failed</string>
    <string name="connecting">Connecting...</string>
    <string name="connection_failed">Connection Failed</string>
    <string name="contact_way">Communication Method</string>
    <string name="please_input_contact">Please enter contact information</string>
    <string name="new_tag">New</string>
    <string name="power_station_system">Power Station System</string>
    <string name="street_name">Street Location</string>
    <string name="owner_info">Owner Info</string>
    <string name="owner_name">Owner Name</string>
    <string name="owner_gender">Owner Gender</string>
    <string name="owner_phone">Owner Phone</string>
    <string name="owner_email">Owner Email</string>
    <string name="input_owner_name">Please enter name</string>
    <string name="input_owner_phone">Please enter phone number</string>
    <string name="input_owner_email">Please enter email</string>
    <string name="select_gender">Select Gender</string>
    <string name="male">Male</string>
    <string name="female">Female</string>
    <string name="error_empty_name">Please enter owner name</string>
    <string name="error_empty_gender">Please select owner gender</string>
    <string name="error_empty_phone">Please enter owner phone number</string>
    <string name="error_invalid_phone">Please enter a valid phone number</string>
    <string name="error_invalid_email">Please enter a valid email format</string>
    <string name="station_image">Station Image</string>
    <string name="upload_station_image">Upload 1 panoramic image of the station</string>
    <string name="config_layout">Config Layout</string>
    <string name="station_info">Station Info</string>
    <string name="region">Region Location</string>
    <string name="power">Installed Capacity</string>
    <string name="power_generation">Generation</string>
    <string name="status">Status</string>
    <string name="please_input_format">Please input %s</string>
    <string name="loading">Loading...</string>
    <string name="operation_success">Operation Success</string>
    <string name="operation_failed">Operation Failed</string>
    <string name="station_status">Station Status</string>
    <string name="station_power">Station Power</string>
    <string name="station_generation">Station Generation</string>
    <string name="please_wait">Please wait...</string>

    <string name="day">Day</string>
    <string name="month">Month</string>
    <string name="year">Year</string>
    <string name="all">All</string>
    <string name="data_statistics">Power Generation data</string>
    <string name="social_contribution">Social Contribution</string>
    <string name="current_power">Current Power</string>
    <string name="daily_total_power">Daily Total Power</string>
    <string name="daily_power_generation">Daily Power Generation</string>
    <string name="total_power_generation">Total Power Generation</string>
    <string name="daily_income">Daily Income</string>
    <string name="total_income">Total Income</string>
    <string name="reduce_co2">Total CO₂ Reduction</string>
    <string name="save_coal">Total Coal Saving</string>

    <string name="shade_ratio">Shade Ratio Setting</string>
    <string name="delay_shutdown">Delay Shutdown Setting</string>
    <string name="right_arrow">Arrow</string>

    <string name="unit_kw">kw</string>
    <string name="unit_w">w</string>
    <string name="unit_kwh">kwh</string>
    <string name="unit_t">T</string>
    <string name="unit_yuan">CNY</string>

    <string name="line_icon">Divider Line</string>

    <string name="unit_celsius">°C</string>
    <string name="unit_minute">Min</string>
    <string name="unit_percent">%</string>

    <string name="button_icon">Button Icon</string>

    <string name="sunrise_time">Sunrise Time</string>
    <string name="sunset_time">Sunset Time</string>

    <string name="email">Email</string>
    <string name="temperature">Temperature</string>

    <string name="no_warnings">Station normal, no warnings to handle</string>
    <string name="temp_warning_count">%d temperature warnings</string>
    <string name="power_warning_count">%d power abnormal warnings</string>
    <string name="temp_shutdown_count">%d high temperature shutdown warnings</string>
    <string name="reminder">Reminder</string>
    <string name="warning_notification">Warning Alert</string>

    <string name="version_update">Version Update</string>
    <string name="version_check">Version Check</string>
    <string name="current_version_short">Current</string>
    <string name="current_version">Current Version: %s</string>
    <string name="latest_version">Latest Version: %s</string>
    <string name="update_content">Update Content</string>
    <string name="update_now">Update Now</string>
    <string name="update_later">Update Later</string>
    <string name="download_now">Download now:</string>
    <string name="download_ok">Download completed,click to install the new version</string>

    <string name="station_view">Station View</string>
    <string name="refresh_component_data">Refresh Component Data</string>
    <string name="reset_component_position">Reset Component Position</string>
    <string name="operation_success_refresh_later">Operation successful, please refresh later</string>
    <string name="component_mode">Component Mode</string>
    <string name="string_mode">String Mode</string>

    <string name="component_number_format">Component No. %s</string>
    <string name="turn_on">Turn On</string>
    <string name="turn_off">Turn Off</string>
    <string name="group_belong">Group</string>
    <string name="component_status">Status</string>
    <string name="status_off">Off</string>
    <string name="status_warning">Warning</string>
    <string name="output_voltage">Output Voltage</string>
    <string name="output_power">Output Power</string>
    <string name="component_temperature">Temperature</string>
    <string name="co2_reduction">CO₂ Reduction</string>
    <string name="voltage_format">%s V</string>
    <string name="power_unit_format">%s W</string>
    <string name="temperature_unit_format">%d °C</string>
    <string name="co2_format">%s G</string>

    <string name="confirm_logout">Confirm logout?</string>
    <string name="close">Close</string>
    <string name="value_unit_format">%1$s %2$s</string>
    <string name="capacity_format">%d kw</string>
    <string name="running_days_format">Running for %d days</string>
    <string name="scan_description">Scan</string>
    <string name="plus_description">Add</string>
    <string name="total">Total</string>
    <string name="expand">Expand</string>
    <string name="collapse">Collapse</string>

    <string name="power_abnormal">Power Abnormal</string>
    <string name="temp_warning_resolved">Temperature Warning Resolved</string>
    <string name="power_restored">Power Restored</string>
    <string name="auto_open">Auto Open</string>
    <string name="warning">Warning</string>
    <string name="sunUptime">Data Collection Start Time</string>
    <string name="sunDowntime">Data Collection End Time</string>

    <string name="confirm_turn_on_optimizer">Confirm to turn on optimizer %s？</string>
    <string name="confirm_turn_off">Confirm to turn off optimizer %s？</string>
   <string name="conventional_string">Conventional Group</string>
    <string name="smart_string">Smart Group</string>

    <string name="component_voltage">Voltage</string>

    <string name="country">Country</string>
    <string name="city">City</string>
    <string name="district">District</string>
    <string name="selected_region">Selected region</string>

    <string name="reset_password">Reset Password</string>
    <string name="input_new_password_again">Please confirm new password</string>
    <string name="resetting_password">Resetting password...</string>
    <string name="password_not_match_tip">Passwords do not match, please modify!</string>
    <string name="verification_code">Verification Code</string>
    <string name="input_verification_code">Please enter verification code</string>

    <string name="verify_user">Verify User</string>
    <string name="get_code_failed">Failed to get verification code</string>
    <string name="get_code_success">get verification code successful</string>

    <string name="reset_password_failed">Failed to reset password</string>
    <string name="reset_password_success">Password reset successful</string>
    <string name="verify_code_success">Verification code sent</string>
    <string name="countdown_format">%ds</string>

    <string name="station_type">Station Type</string>
    <string name="select_station_type">Select Station Type</string>
    <string name="type_2_4g">2.4G</string>
    <string name="type_plc">PLC</string>
    <string name="time_error">End time cannot be earlier than start time</string>

    <string name="remember_credentials">Remember name and pass</string>

    <string name="input_collector_name">Please enter collector name</string>
    <string name="input_group_name">Please enter group name</string>
    <string name="input_optimizer_id">Please enter optimizer ID</string>
    <string name="input_group_name_hint">E.g., Station Group 1, Station Group 2...</string>
    <string name="input_power_hint">E.g., 3, 5, 10, 20, 30...</string>
    <string name="power_kwp">Group Power (kWp)</string>

    <string name="collector">Collector</string>
    <string name="group">Group</string>
    <string name="optimizer">Optimizer</string>

    <string name="contact_us">Contact Us</string>
    <string name="user_manual">User Manual</string>
    <string name="send_verification_code">send sms code</string>
    <string name="account_verification">Account Verification</string>

    <string name="weather_greeting">hello, today\'s weather is%s</string>

    <string name="component_power_percent">Power Percentage</string>
    <string name="component_generation_percent">Generation</string>

    <!-- Server Settings -->
    <string name="server_1">Server 1</string>
    <string name="server_2">Server 2</string>
    <string name="server_3">Server 3</string>

    <string name="physical_view">Physical View</string>
    <string name="logical_view">Logical View</string>

    <string name="inverter">Inverter</string>
    <string name="inverter_name">Inverter Name</string>
    <string name="verify_authenticity">Anti counterfeiting verification</string>

    <string name="query">Query</string>
    <string name="wifi_status">WiFi Status</string>

    <!-- Relay settings -->
    <string name="relay_settings">Relay Settings</string>
    <string name="relay_1">Relay 1</string>
    <string name="relay_2">Relay 2</string>
    <string name="relay_3">Relay 3</string>
    <string name="relay_4">Relay 4</string>
    <string name="relay_5">Relay 5</string>
    <string name="relay_6">Relay 6</string>
    <string name="relay_7">Relay 7</string>
    <string name="relay_8">Relay 8</string>
    <string name="save_relay">Save Relay Settings</string>

    <string name="collector_config">Collector Configuration</string>
    <string name="tab_setting">Setting</string>
    <string name="tab_query">Query</string>

    <!-- Server Settings -->
    <string name="server_settings">Server Settings</string>
    <string name="server_ip">Server IP</string>
    <string name="server_port">Port</string>
    <string name="save_server">Save Server Settings</string>

    <string name="relay">Relay</string>
    <string name="edit_relay">Edit Relay</string>
    <string name="relay_count">Relay Count:</string>
    <string name="input_relay_name">Please enter relay name</string>

    <string name="relay_info">Relay Info</string>
    <string name="relay_name">Relay Name</string>
    <string name="relay_id">Relay ID</string>
    <string name="input_relay_id">Please enter relay ID</string>

    <string name="relay_config">Relay Config</string>

    <string name="query_window_placeholder">Please select an operation below...</string>
    <string name="execute_operation">Execute Operation</string>

    <string name="operate_relay">Operating Relay</string>
    <string name="operate_type">Operation Type</string>
    <string name="select_relay">Select Relay</string>
    <string name="select_operation">Select Operation Type</string>
    <string name="optimizer_burn">Optimizer Burning</string>
    <string name="optimizer_id_query">Optimizer ID Query</string>
    <string name="manual_networking">Manual Networking</string>
    <string name="network_result_query">Network Result Query</string>
    <string name="optimizer_param_query">Optimizer Electrical Parameter Cache Query</string>

    <string name="execute">Execute</string>

    <string name="please_connect_wifi">Please connect to WiFi and try again</string>
    <string name="please_connect_correct_wifi">Please connect to the correct WiFi and try again</string>

    <string name="turn_off_groups">Turn Off Groups and Station</string>
    <string name="turn_on_groups">Turn On Groups and Station</string>

    <string name="confirm_turn_off_groups">Confirm to turn off group: %s?</string>
    <string name="confirm_turn_on_groups">Confirm to turn on group: %s?</string>
    <string name="confirm_turn_off_station">Confirm to turn off station: %s</string>
    <string name="confirm_turn_on_station">Confirm to turn on station: %s</string>

    <string name="turn_off_group">Turn Off Group</string>
    <string name="turn_on_group">Turn On Group</string>
    <string name="group_operation">Group Operation</string>

    <string name="select_all">Select All</string>
    <string name="confirm_operation">Confirm %1$s %2$s?</string>

    <string name="add_to_relay">Add to Relay</string>
    <string name="remove_from_relay">Remove from Relay</string>

    <string name="add_optimizer">Add Optimizer</string>
    <string name="remove_optimizer">Remove Optimizer</string>
    <string name="group_way">Group Way</string>
    <string name="optimizer_way">Optimizer Way</string>

    <string name="status_closed">Closed</string>

    <string name="list_is_empty">列表为空</string>
    <string name="response_error">网络请求错误，请稍后重试</string>
    <string name="network_error1">网络连接失败，请稍后重试</string>
    <string name="request_time_out">网络请求超时，请稍后重试</string>
    <string name="request_web">请求网络中…</string>
    <string name="noMoreData">没有更多数据</string>
    <string name="title_activity_title">Title</string>

    <string name="home_title">Home</string>
    <string name="please_select">Please Select</string>

    <!-- Register Related -->
    <string name="sms_register">SMS Register</string>
    <string name="input_phone">Please enter phone number</string>

    <!-- 抽检测试相关字符串 -->
    <string name="sampling_test">Sampling Test</string>
    <string name="open_optimizer">Open Optimizer</string>
    <string name="close_optimizer">Close Optimizer</string>
    <string name="query_electrical_info">Query Electrical Info</string>
    <string name="select_optimizer">Select Optimizer</string>
    <string name="operate_optimizer">Operating Optimizer</string>

    <string name="send_log">Send Log</string>

    <string name="please_input_account">Please enter account</string>
    <string name="please_input_verification_code">Please enter verification code</string>
    <string name="field_required">This field is required</string>
    
    <!-- Register Page -->
    <string name="country_and_region">Country and Region</string>
    <string name="please_enter">Please enter</string>
    <string name="company_name">Company Name</string>
    <string name="company_name_hint">Required for dealers/installers, optional for owners</string>
    <string name="account_register">Account Registration</string>
    <string name="please_input_email_phone">Please enter email/phone</string>
    <string name="send_verification_code_btn">Send Code</string>
    <string name="password_settings">Password Settings</string>
    <string name="please_input_password_hint">Please enter 6-20 digit password</string>
    <string name="please_input_password_again">Please enter 6-20 digit password again</string>
    <string name="i_have_read_and_agree">I have read and agree</string>
    <string name="service_agreement">Service Term</string>
    <string name="privacy_agreement">Privacy Policy</string>
    <string name="register_now">Register Now</string>

    <string name="seconds_later">%1$ds later</string>

    <string name="optimizer_sn">Optimizer S/N</string>
    <string name="optimizer_model">Optimizer Model</string>
    <string name="input_or_scan_optimizer_sn">Please enter/scan the device</string>
    <string name="error_optimizer_sn_empty">Please enter Optimizer S/N</string>
    <string name="error_optimizer_sn_starts_with_zero">Optimizer ID cannot start with 0</string>
    <string name="error_optimizer_sn_invalid_chars">Optimizer ID can only contain numbers and letters a-f</string>
    <string name="error_optimizer_sn_invalid_value">The hexadecimal value of the Optimizer ID must be greater than 20000</string>
    <string name="select_gender_hint">Select: Male/Female</string>
    <string name="submit_info">Submit Information</string>
    <string name="error_optimizer_model_empty">Please select the model</string>
    <string name="error_invalid_input">Please check the input information</string>

    <!-- GroupOnePage Specific -->
    <string name="error_group_name_empty">Please enter group name</string>
    <string name="error_power_empty">Please enter power value</string>
    <string name="error_power_invalid">Please enter a valid power number</string>

    <!-- RelayOnePage Specific -->
    <string name="error_relay_name_empty">Please enter relay name</string>
    <string name="error_relay_id_empty">Please enter relay ID</string>
    <string name="error_relay_id_invalid_chars">Relay ID can only contain numbers and letters a-f</string>
    <string name="input_or_scan_relay_id">Please enter/scan the device</string>

    <!-- CollectorOnePage Specific -->
    <string name="input_or_scan_imei">Please enter/scan IMEI</string>
    <string name="error_collector_name_empty">Please enter collector name</string>
    <string name="error_imei_empty">Please enter IMEI</string>
    <string name="error_imei_invalid_chars">IMEI can only contain numbers</string>

    <string name="select_station">Select Station</string>
    <string name="select_device">Select Device</string>
    <string name="issue_description">Issue Description</string>
    <string name="error_select_station">Please select a station</string>
    <string name="error_select_device">Please select a device</string>
    <string name="error_input_issue">Please enter issue description</string>
    <string name="error_input_contact">Please enter contact information</string>
    <string name="add_image">Add Image</string>


    <string name="please_select_relay">Please select relay</string>
    <string name="please_select_operation">Please select operation type</string>
    <string name="relay_error_empty">Relay cannot be empty</string>
    <string name="operation_error_empty">Operation type cannot be empty</string>
    <string name="query_window">Query Window</string>

    <!-- Add Station Page -->
    <string name="location_address">Location</string>
    <string name="station_name">Station Name</string>
    <string name="error_empty_field">This field cannot be empty</string>
    <string name="error_invalid_power">Please enter a valid power value</string>
    <string name="error_station_time">Sunset time cannot be earlier than sunrise time</string>

    <!-- Newly added strings for Add Station Page -->
    <string name="power_price_config">Power Price Config</string>
    <string name="next_step">Next Step</string>
    <string name="collect_time">Data Collection Time</string>
    <string name="input_address_details">Street/Community/House number detailed address</string>
    <string name="input_power_sample">Please fill in, e.g., 5, 10...</string>
    <string name="online_time">Grid Connection Time</string>
    <string name="online_type">Grid Connection Type</string>
    <string name="date_format_sample">YYYY/MM/DD</string>
    <string name="sunrise_time_format">Sunrise Time HH:mm</string>
    <string name="sunset_time_format">Sunset Time HH:mm</string>
    <string name="province_city_district_placeholder">Province/City/District</string>
    <string name="sunrise_placeholder_format">Sunrise Time 06:00</string>
    <string name="sunset_placeholder_format">Sunset Time 18:00</string>

    <!-- DeviceConfigScreen -->
    <string name="device_config">Device Configuration</string>
    <string name="add_device">Add Device</string>
    <string name="belongs_to_station">Station: {station}</string>
    <string name="smart_gateway">Smart Gateway</string>
    <string name="device_config_button">Device Config</string>
    <string name="please_input">Please Input</string>
    <string name="imei_number">IMEI#: {imei}</string>
    <string name="device_count">Count: {count}</string>
    <string name="smart_optimizer">Smart Optimizer</string>
    <string name="station_group">Station Group</string>
    <string name="optimizer_count_text">Optimizer Count: {count}</string>
    <string name="group_count">Group Count: {count}</string>

    <!-- GroupConfigScreen -->
    <string name="group_config">Group Configuration</string>
    <string name="existing_optimizers">Existing Optimizers: {count}</string>
    <string name="creation_date">Creation Date: {date}</string>

    <string name="smart_gateway_config">Smart Gateway Config</string>
    
    <!-- OptimizerConfigScreen -->
    <string name="optimizer_config">Optimizer Configuration</string>

    <!-- Station operation related strings -->
    <string name="turn_on_station">Turn On Station</string>
    <string name="turn_off_station">Turn Off Station</string>
    <string name="turn_on_station_warning">This operation will turn on all components and devices of the current station!</string>
    <string name="turn_off_station_warning">This operation will turn off all components and devices of the current station!</string>
    <string name="continue_turn_on">Continue to turn on</string>
    <string name="continue_turn_off">Continue to turn off</string>
    <string name="warning_title_str">Warning</string>
    <string name="confirm_title_str">Confirm Turn Off</string>
    <string name="turn_on_station_warning_second">All components and devices of the current station will start working! Please proceed with caution</string>
    <string name="turn_off_station_warning_second">All components and devices of the current station will stop working! Please proceed with caution</string>

    <!-- Account Type Selection Page -->
    <string name="account_type">Account Type</string>
    <string name="account_type_owner">Owner User</string>
    <string name="account_type_installer">Dealer/Installer</string>
    <string name="account_type_owner_desc">New/Existing station owner</string>
    <string name="account_type_installer_desc">User providing services to owners</string>
    <string name="select_account_type">Please select account type</string>

    <!-- One-key operation string resources -->
    <string name="one_key_turn_on">One-key Turn On</string>
    <string name="one_key_turn_off">One-key Turn Off</string>

    <!-- Searchbox placeholder text -->
    <string name="input_collector_id">Enter Smart Gateway ID</string>
    <string name="input_group_name_search">Enter Group Name</string>
    <string name="input_optimizer_sn">Enter Optimizer S/N</string>
    <string name="input_relay_sn">Enter Relay S/N</string>

    <string name="optimizer_shutdown_switch">Optimizer Shutdown Switch</string>
    <string name="group_shutdown_switch">String Shutdown Switch</string>

</resources>