package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.ComponentItem
import com.ymx.photovoltaic.ui.page.widget.CustomMenuItem
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector
import photovoltaic_kmp_app.composeapp.generated.resources.edit_collector
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.input_collector_name
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.relay

// 进入这个页面会加载所有tab页的请求
@Composable
fun EquipmentTabPage(
    navHostController: NavHostController,
    stationName: String,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
    val tabs = listOf(
        stringResource(Res.string.collector),
        stringResource(Res.string.group),
        stringResource(Res.string.optimizer),
        stringResource(Res.string.relay)
    )
    
    val pagerState = rememberPagerState(initialPage = 0) { tabs.size }
    val coroutineScope = rememberCoroutineScope()
    val collectorList by equipmentViewModel.collectorListFlow.collectAsState()

    // 目前一个电站只支持一个采集器，所以目前取第一个采集器的imei即可
    val firstCollectorId = collectorList.firstOrNull()?.id ?: AppGlobal.powerStationId
    val imei = collectorList.firstOrNull()?.imei ?: ""
    AppGlobal.imei = imei
    
    // 在页面加载时获取采集器列表和所有中继器的优化器数据
    LaunchedEffect(Unit) {
        equipmentViewModel.fetchCollectorList(AppGlobal.powerStationId)
    }
    
    // 当采集器列表加载完成且有采集器时，获取所有中继器的优化器数据
    LaunchedEffect(collectorList) {
        if (collectorList.isNotEmpty() && imei.isNotEmpty()) {
            equipmentViewModel.fetchAllRelayComponentData(
                cloudId = imei,
                createUserId = AppGlobal.mId
            )
        }
    }

    Scaffold(
        topBar = {
            Column {
                TopBar(stationName, 
                    backClick = { navHostController.popBackStack() })
                TabRow(
                    selectedTabIndex = pagerState.currentPage,
                    containerColor = Color.White,
                    contentColor = Color.Black
                ) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            text = { 
                                Text(
                                    text = title,
                                    fontSize = 16.sp,
                                ) 
                            },
                            selected = pagerState.currentPage == index,
                            onClick = {
                                coroutineScope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            }
                        )
                    }
                }
            }
        },
        containerColor = Grey_F5,
        floatingActionButton = {
            when (pagerState.currentPage) {
                0 -> {
                    if(collectorList.isEmpty()) {
                        FloatingButton{
                            navHostController.navigate(Route.COLLECTOR_NEW)
                        }
                    }
                }

                1 -> {
                    if(collectorList.isNotEmpty())
                    {
                        FloatingButton{
                            navHostController.navigate(Route.GROUP_NEW+"/$firstCollectorId")
                        }
                    }

                }
                // 当页面为2时不显示FloatingButton
                3 -> {
                    if(collectorList.isNotEmpty())
                    {
                        FloatingButton{
                            navHostController.navigate(Route.RELAY_NEW)
                        }
                    }

                }

            }
        }
    ) { paddingValues ->
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.padding(paddingValues)
        ) { page ->
            when (page) {
                0 -> CollectorContent(navHostController, equipmentViewModel)
                1 -> GroupContent(
                    navHostController = navHostController,
                    collectorId = firstCollectorId,
                    equipmentViewModel = equipmentViewModel,
                    modifier = Modifier
                )
                2 -> OptimizerContent(
                    navHostController = navHostController,
                    groupId = null,
                    powerStationId = AppGlobal.powerStationId,
                    belongsGroupFlagStr = "",
                    equipmentViewModel = equipmentViewModel,
                    modifier = Modifier
                )
                3 -> RelayContent(
                    navHostController = navHostController,
                    powerId = AppGlobal.powerStationId,
                    equipmentViewModel = equipmentViewModel,
                    modifier = Modifier
                )
            }
        }
    }
}

@Composable
private fun CollectorContent(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel
) {
    val collectorList by equipmentViewModel.collectorListFlow.collectAsState()
    val relayList by equipmentViewModel.relayListFlow.collectAsState()
    val relayChipIdMap by equipmentViewModel.relayChipIdMapFlow.collectAsState()
    var searchQuery by remember { mutableStateOf("") }

    val filteredCollectors = collectorList.filter {
        it.cloudName.contains(searchQuery, ignoreCase = true)
    }

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchCollectorList(AppGlobal.powerStationId)
    }

    Column {
        Spacer(modifier = Modifier.height(5.dp))
        CommonSearch(
            searchQuery = searchQuery,
            onQueryChange = { searchQuery = it },
            placeholderTextResId = Res.string.input_collector_name,
            Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(55.dp)
                .clip(RoundedCornerShape(25.dp))
        )
        val newCollectorList = filteredCollectors ?: emptyList()

        LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
            items(newCollectorList) { collector ->
                val customMenuItems = listOf(
                    CustomMenuItem(stringResource(Res.string.edit_collector)) {
                        navHostController.navigate(
                            Route.COLLECTOR_EDIT + "/${collector.id}/${collector.cloudName}/${collector.imei}"
                        )
                    }
                )
                ComponentItem(
                    type = "collector",
                    titleOne = collector.cloudName,
                    titleTwo = collector.imei,
                    menuItems = customMenuItems,
                    onClick = {
                        navHostController.navigate(Route.GROUP + "/${collector.id}")
                    },
                    onFirstConfigClick = {
                        navHostController.navigate(Route.SET_COLLECTOR)
                    },
                    onSecondConfigClick = {
                        // 将数据保存到 AppGlobal 中
                        AppGlobal.relayList = relayList
                        AppGlobal.relayChipIdMap = relayChipIdMap
                        
                        // 导航到 SetRelayPage
                        navHostController.navigate(Route.SET_RELAY + "/${collector.imei}")
                    }
                )
            }
        }
    }
}



