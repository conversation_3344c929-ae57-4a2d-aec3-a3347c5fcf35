package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.plus

@Composable
fun ConfirmButton( showText: String,
                   enabledBoolean: Boolean,
                   onItemClick: () -> Unit)
{
    Box(
        modifier = Modifier
            .height(50.dp)
            .background(
                brush = Brush.verticalGradient(
                    0.4468f to Color(0xFFEF5864),
                    100f to Color(0xFFE60012),
                ),
                shape = RoundedCornerShape(25.dp)
            )
    ){

        Button(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                disabledContainerColor = Color.Transparent,
                disabledContentColor = Color.Transparent
            ),
            enabled = enabledBoolean,
            onClick = {
                onItemClick()}
        ) {
            Text(text = showText,
                color = Color.White
            )
        }
    }

}

@Composable
fun FloatingButton(onClick: () -> Unit)
{
    FloatingActionButton(
        // 设置阴影和边框效果
        modifier = Modifier.shadow(
            elevation = 8.dp,
            shape = CircleShape,
            spotColor = Color.Black.copy(alpha = 0.25f),
            ambientColor = Color.Black.copy(alpha = 0.25f)
        )// 设置图标大小
            .clip(CircleShape).border(
                width = 0.2.dp,
                color = Color.Black.copy(alpha = 0.1f),
                shape = CircleShape
            ),
        containerColor = Color.White,
        elevation = FloatingActionButtonDefaults.elevation(8.dp),
        onClick = {onClick()}
    ) {
        Icon(
            painterResource(Res.drawable.plus), "Add",
            tint = Color(50,116,249,255),
        )
    }

}