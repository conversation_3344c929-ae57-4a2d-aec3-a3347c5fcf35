package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.please_select

@Composable
fun CommonSelector(
    regionValue: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val pleaseSelect = stringResource(Res.string.please_select)
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(45.dp)
            .background(color = Color.White, shape = RoundedCornerShape(30.dp))
            .clickable { onClick() }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .height(45.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = regionValue,
                color = if (regionValue == pleaseSelect) Color.Gray else Color.Black,
                fontSize = 14.sp
            )
            Text(
                text = ">",
                color = Color.Gray,
                fontSize = 18.sp
            )
        }
    }
}