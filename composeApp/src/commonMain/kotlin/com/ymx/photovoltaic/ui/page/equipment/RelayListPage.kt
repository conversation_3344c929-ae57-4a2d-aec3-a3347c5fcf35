package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.ComponentItem
import com.ymx.photovoltaic.ui.page.widget.CustomMenuItem
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.edit_relay
import photovoltaic_kmp_app.composeapp.generated.resources.input_relay_name
import photovoltaic_kmp_app.composeapp.generated.resources.relay

@Composable
fun RelayPage(
    navHostController: NavHostController,
    powerId: String,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.relay),
                backClick = { navHostController.popBackStack() })
        },
        backgroundColor = Color(242, 243, 245, 1),
        floatingActionButton = {
            FloatingButton{ 
                navHostController.navigate(Route.RELAY_NEW)
            }
        }
    ) { paddingValues ->
        RelayContent(
            navHostController = navHostController,
            powerId = powerId,
            equipmentViewModel = equipmentViewModel,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

@Composable
fun RelayContent(
    navHostController: NavHostController,
    powerId: String,
    equipmentViewModel: EquipmentViewModel,
    modifier: Modifier = Modifier
) {
    val relayList by equipmentViewModel.relayListFlow.collectAsState()


    var searchQuery by remember { mutableStateOf("") }

    val filteredRelays = relayList.filter {
        it.relayName.contains(searchQuery, ignoreCase = true)
    }

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchRelayList(AppGlobal.mId, "view", powerId)
    }

    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(5.dp))
        CommonSearch(
            searchQuery = searchQuery,
            onQueryChange = { searchQuery = it },
            placeholderTextResId = Res.string.input_relay_name,
            Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(55.dp)
                .clip(RoundedCornerShape(25.dp))
        )

        val newRelayList = filteredRelays ?: emptyList()

        LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
            items(newRelayList) { relay ->
                val customMenuItems = listOf(
                    CustomMenuItem(stringResource(Res.string.edit_relay)) {
                        navHostController.navigate(
                            Route.RELAY_EDIT + "/${relay.relayId}/${relay.relayName}/${relay.id}"
                        )
                    }
                )
                ComponentItem(
                    "relay",
                    relay.relayName,
                    relay.relayId,
                    menuItems = customMenuItems,
                    onClick = {
                        navHostController.navigate(Route.OPTIMIZER_FOR_RELAY+"/${relay.relayId}")
                    },
                    onFirstConfigClick = {
                        navHostController.navigate(
                            Route.RELAY_OPTIMIZER_SELECT + "/${relay.relayId}/${AppGlobal.mId}/1"
                        )
                    },
                    onSecondConfigClick = {
                        navHostController.navigate(
                            Route.RELAY_OPTIMIZER_SELECT + "/${relay.relayId}/${AppGlobal.mId}/2"
                        )
                    }
                )
            }
        }
    }
} 