package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource

@Composable
fun CustomCardWithCutCorner(
    imageResId: DrawableResource,
    cardColor:Color,
    value:String,
    unit:String,
    description:String,
    boxModifier: Modifier
) {
    Box(
        modifier = boxModifier
    ) {
        // 圆角矩形卡片
        Card(
            modifier = Modifier.width(160.dp).height(80.dp),
            colors = CardDefaults.cardColors(containerColor = cardColor)
        ) {
            // 卡片内容可以在这里添加
            Box(
                modifier = Modifier
                    .padding(start = 16.dp,top=10.dp, bottom =10.dp),
            ) {
                ContributionItem(value = value, unit = unit, description = description)

            }
        }

        // 在卡片右上角放置图片，实现“缺角”效果
        Image(
            painter = painterResource(imageResId),
            contentDescription = null,
            modifier = Modifier
                .size(30.dp) // 为图片设置圆角
                .align(Alignment.TopEnd) // 偏移图片位置，使其位于卡片右上角
        )
    }
}


