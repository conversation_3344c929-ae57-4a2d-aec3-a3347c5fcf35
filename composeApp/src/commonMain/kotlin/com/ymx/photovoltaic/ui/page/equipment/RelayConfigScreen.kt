package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.relay
import photovoltaic_kmp_app.composeapp.generated.resources.input_relay_sn

/**
 * 中继配置页面
 */
@Composable
fun RelayConfigScreen(
    navHostController: NavHostController,
    relayList: List<Relay>,
    imei: String = ""
) {

    var searchQuery by remember { mutableStateOf("") } // 用于存储搜索框中的输入

    val filteredItems = relayList.filter {
        it.relayId.contains(searchQuery, ignoreCase = true)
        // 只过滤名字字段
    }

    Scaffold(
        topBar = {
            TopBar(
                textStr = "中继器配置",
                backClick = { navHostController.popBackStack() },
            )
        },
        containerColor = Grey_F5,
        floatingActionButton = {
            FloatingButton(
                onAddClicked = {
                navHostController.navigate("${Route.RELAY_NEW}/$imei")
                },

            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 0.dp)
        ) {

            CommonSearch(searchQuery, onQueryChange = { searchQuery = it }, 
                placeholderTextResId = Res.string.input_relay_sn,
                modifier = Modifier.
                padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 10.dp).
                fillMaxWidth().height(48.dp)
                .clip(RoundedCornerShape(25.dp)) )

            LazyColumn {
                items(filteredItems) { relay ->
                    DeviceItem(
                        imagePainter = painterResource(Res.drawable.relay),
                        secondRowText = "型号: gd",
                        firstRow = {
                            DeviceTextButtonRow(
                                textStr = "S/N:${relay.relayId}",
                                buttonText = "新增优化器",
                                onButtonClick = {
                                    navHostController.navigate(
                                        Route.RELAY_OPTIMIZER_SELECT + "/${relay.relayId}/${AppGlobal.mId}/1"
                                    )
                                }
                            )
                        },
                        lastRow = {
                            DeviceTextButtonRow(
                                textStr = "创建时间:${relay.createTime}",
                                buttonText = "删除优化器",
                                onButtonClick = {
                                    navHostController.navigate(
                                        Route.RELAY_OPTIMIZER_SELECT + "/${relay.relayId}/${AppGlobal.mId}/2"
                                    )
                                },
                                isTextBold = false,
                                buttonBorderColor = Color.Red
                            )
                        },
                    )
                }
            }

        }
    }
} 