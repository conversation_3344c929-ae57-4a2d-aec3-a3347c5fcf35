package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.ymx.photovoltaic.ui.page.theme.LjRed

@Composable
fun AccountVerificationInput(
    account: String,
    onAccountChange: (String) -> Unit,
    isAccountError: Boolean,
    accountErrorText: String,
    countdown: Int,
    onSendCodeClick: () -> Unit,
    sendCodeButtonText: String,
    placeholderText: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(16.dp))
        ) {
            OutlinedTextField(
                value = account,
                onValueChange = onAccountChange,
                placeholder = { Text(placeholderText) },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color.White,
                    unfocusedContainerColor = Color.White,
                    focusedIndicatorColor = if (isAccountError) Color.Red else Color.Transparent,
                    unfocusedIndicatorColor = if (isAccountError) Color.Red else Color.Transparent,
                    errorContainerColor = Color.White,
                    errorIndicatorColor = Color.Red
                ),
                trailingIcon = {
                    OutlinedButton(
                        onClick = onSendCodeClick,
                        enabled = account.isNotEmpty() && countdown == 0,
                        border = BorderStroke(1.dp, LjRed),
                        modifier = Modifier.padding(end = 8.dp),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = if (countdown > 0) "${countdown}s" else sendCodeButtonText,
                            color = LjRed
                        )
                    }
                },
                isError = isAccountError
            )
        }

        if (isAccountError) {
            Text(
                text = accountErrorText,
                color = Color.Red,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        }
    }
} 