package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.error_relay_id_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_relay_id_invalid_chars
import photovoltaic_kmp_app.composeapp.generated.resources.error_relay_name_empty
import photovoltaic_kmp_app.composeapp.generated.resources.input_or_scan_relay_id
import photovoltaic_kmp_app.composeapp.generated.resources.relay_id
import photovoltaic_kmp_app.composeapp.generated.resources.relay_info
import photovoltaic_kmp_app.composeapp.generated.resources.relay_name
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.scan

@Composable
fun RelayOnePage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get(),
    relayId: String = "",
    relayName: String = "",
    imei: String = "",
    id: Int? = null,
    powerStationId: String = AppGlobal.powerStationId
) {
    var relayNameValue by remember { mutableStateOf(relayName) }
    var relayIdValue by remember { mutableStateOf(relayId) }
    
    // Error states
    var relayNameError by remember { mutableStateOf<String?>(null) }
    var relayIdError by remember { mutableStateOf<String?>(null) }

    val scanResult =  navHostController.currentBackStackEntry?.savedStateHandle?.
    getStateFlow("scanResult","")?.collectAsState()

    LaunchedEffect (scanResult?.value) {
        val  relayIdStr=scanResult?.value.toString()
        if(relayIdStr.isNotEmpty())
        {
            val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f')
            relayIdValue = relayIdStr.lowercase().filter { it in validChars }
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.relay_info), backClick = { navHostController.navigateUp() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }

            var showFailDialog by remember { mutableStateOf(false) }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 中继器名称输入
            CommonTitleField(
                value = relayNameValue,
                onValueChange = { 
                    relayNameValue = it
                    relayNameError = null // Clear error when user types
                },
                titleText = stringResource(Res.string.relay_name),
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = relayNameError != null,
                errorText = relayNameError ?: ""
            )

            // 中继器ID输入
            CommonTitleField(
                value = relayIdValue,
                onValueChange = { newValue ->
                    // 只保留有效字符
                    val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                        'a', 'b', 'c', 'd', 'e', 'f')
                    relayIdValue = newValue.lowercase().filter { it in validChars }
                    relayIdError = null // Clear error when user types
                },
                titleText = stringResource(Res.string.relay_id),
                placeholderCom = {
                    PlaceholderText(Res.string.input_or_scan_relay_id)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(
                    capitalization = KeyboardCapitalization.None,
                    autoCorrectEnabled = false,
                    keyboardType = KeyboardType.Text
                ),
                isError = relayIdError != null,
                errorText = relayIdError ?: "",
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.scan),
                        modifier = Modifier.size(18.dp).clickable {
                            // 跳转到扫描页面
                            navHostController.navigate(Route.SCAN)
                        },
                        contentDescription = "Scan ID"
                    )
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()

            val errorRelayNameEmpty=stringResource(Res.string.error_relay_name_empty)
            val errorRelayIdEmpty=stringResource(Res.string.error_relay_id_empty)

            val errorRelayIdInvalidChars=stringResource(Res.string.error_relay_id_invalid_chars)


            ConfirmButton(stringResource(Res.string.save), true) {
                // Reset errors
                relayNameError = null
                relayIdError = null
                var hasError = false

                val trimmedRelayName = relayNameValue.trim()
                val trimmedRelayId = relayIdValue.trim()

                // Validate Relay Name
                if (trimmedRelayName.isEmpty()) {
                    relayNameError = errorRelayNameEmpty
                    hasError = true
                }

                // Validate Relay ID
                if (trimmedRelayId.isEmpty()) {
                    relayIdError = errorRelayIdEmpty
                    hasError = true
                } else {
                    val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                        'a', 'b', 'c', 'd', 'e', 'f')
                    if (trimmedRelayId.any { it !in validChars }) {
                        relayIdError = errorRelayIdInvalidChars
                        hasError = true
                    }
                }

                if (hasError) {
                    return@ConfirmButton
                }

                equipmentViewModel.saveOrUpdateRelay(
                    relayId = trimmedRelayId,
                    relayName = trimmedRelayName,
                    imei = imei,
                    powerStationId = powerStationId,
                    createUserId = AppGlobal.mId,
                    id = id,
                    errorBlock = {
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    }
                ) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
    }
}