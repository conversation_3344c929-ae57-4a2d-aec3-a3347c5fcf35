package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.error_group_name_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_invalid_input
import photovoltaic_kmp_app.composeapp.generated.resources.error_power_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_power_invalid
import photovoltaic_kmp_app.composeapp.generated.resources.group_info
import photovoltaic_kmp_app.composeapp.generated.resources.group_name
import photovoltaic_kmp_app.composeapp.generated.resources.input_group_name_hint
import photovoltaic_kmp_app.composeapp.generated.resources.input_power_hint
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.power_kwp
import photovoltaic_kmp_app.composeapp.generated.resources.save


@Composable
fun GroupOnePage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get(),
    groupId:String,
    collectorId:String,
    groupName:String,
    power:String
) {

    var groupNameValue by remember { mutableStateOf(groupName) }
    var powerValue by remember { mutableStateOf(power) }
    var groupNameError by remember { mutableStateOf<String?>(null) }
    var powerError by remember { mutableStateOf<String?>(null) }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.group_info), backClick = {navHostController.popBackStack()})
        },
        containerColor = Grey_F5
    ) {
            paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues).padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {


            var showSuccessDialog by remember { mutableStateOf(false) }

            if (showSuccessDialog) {
                ResultDialog(true){showSuccessDialog = false}
            }

            var showFailDialog by remember { mutableStateOf(false) }

            if (showFailDialog) {
                ResultDialog(false){showFailDialog = false}
            }

            Spacer(modifier = Modifier.height(16.dp))

            val groupNameTitle = stringResource(Res.string.group_name)
            CommonTitleField(
                value = groupNameValue,
                onValueChange = {
                    groupNameValue = it
                    groupNameError = null
                },
                titleText = groupNameTitle,
                placeholderCom = {
                    PlaceholderText(Res.string.input_group_name_hint)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = groupNameError != null,
                errorText = groupNameError ?: ""
            )

            val powerTitle = stringResource(Res.string.power_kwp)
            CommonTitleField(
                value = powerValue,
                onValueChange = { newValue ->
                    if (newValue.all { it.isDigit() }) {
                        powerValue = newValue
                        powerError = null
                    }
                },
                titleText = powerTitle,
                placeholderCom = {
                    PlaceholderText(Res.string.input_power_hint)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                isError = powerError != null,
                errorText = powerError ?: ""
            )

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()
            val errorMsgGroupNameEmpty = stringResource(Res.string.error_group_name_empty)
            val errorMsgPowerEmpty = stringResource(Res.string.error_power_empty)
            val errorMsgPowerInvalid = stringResource(Res.string.error_power_invalid)
            val errorMsgInvalidInput = stringResource(Res.string.error_invalid_input)
            val operationFailedMsg = stringResource(Res.string.operation_failed)

            ConfirmButton(stringResource(Res.string.save), true) {

                groupNameError = null
                powerError = null
                var hasError = false

                val trimmedGroupName = groupNameValue.trim()
                val trimmedPower = powerValue.trim()

                if (trimmedGroupName.isEmpty()) {
                    groupNameError = errorMsgGroupNameEmpty
                    hasError = true
                }

                if (trimmedPower.isEmpty()) {
                    powerError = errorMsgPowerEmpty
                    hasError = true
                } else {
                    try {
                        trimmedPower.toInt()
                    } catch (e: NumberFormatException) {
                        powerError = errorMsgPowerInvalid
                        hasError = true
                    }
                }

                if (hasError) {
                    return@ConfirmButton
                }

                val mutableMap = mutableMapOf(
                    "groupName" to trimmedGroupName,
                    "power" to trimmedPower.toInt()
                )

                if(groupId!= "")
                {
                    mutableMap["id"] = groupId
                }
                else
                {
                    mutableMap["powerStationId"] = AppGlobal.powerStationId
                    mutableMap["createUserId"] = AppGlobal.mId
                    mutableMap["groupType"] = 2.toString()
                    mutableMap["cloudId"] = collectorId
                    mutableMap["groupNo"] = ""
                }


                equipmentViewModel.addOrModGroup(mutableMap, errorBlock =
                    {
                        ToastUtil.showShort(operationFailedMsg)
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    })
                {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
        }
    }