package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusEvent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_hide
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_show

@Composable
fun PasswordTextField( password: String,
                       placeholderString: String,
                       onPasswordChange: (String) -> Unit,
                       passwordVisibility: Boolean,
                       onPasswordVisibilityChange: () -> Unit,
                       modifier: Modifier,
                       isError: Boolean = false,
                       errorText: String = ""
)
{
    OutlinedTextField(
        modifier = modifier,
        value = password,
        placeholder = { 
            Text(
                text = placeholderString,
                fontSize = 14.sp,
                lineHeight = 14.sp
            ) 
        },
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.White,
            unfocusedContainerColor = Color.White,
            focusedIndicatorColor = if (isError) Color.Red else Color.White,
            unfocusedIndicatorColor = if (isError) Color.Red else Color.White,
            errorContainerColor = Color.White,
            errorIndicatorColor = Color.Red
        ),
        visualTransformation = if (passwordVisibility) VisualTransformation.None else PasswordVisualTransformation(),
        trailingIcon = {
            IconButton(onClick = { onPasswordVisibilityChange() }) {
                Icon(
                    painter = if (passwordVisibility) painterResource(Res.drawable.ic_pwd_show)
                    else painterResource(Res.drawable.ic_pwd_hide),
                    modifier = Modifier.size(25.dp),
                    contentDescription = null
                )
            }
        },
        onValueChange = onPasswordChange,
        shape = RoundedCornerShape(16.dp),
        isError = isError
    )
    
    if (isError && errorText.isNotEmpty()) {
        Text(
            text = errorText,
            color = Color.Red,
            modifier = Modifier.padding(start = 16.dp, top = 4.dp)
        )
    }
}



@Composable
fun UserTextField(
    valueStr: String,
    placeholderString: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        modifier = modifier.fillMaxWidth()
            .padding(PaddingValues(horizontal = 20.dp, vertical = 10.dp))
            .background(color = Color.White, shape = RoundedCornerShape(16.dp)),
        value = valueStr,
        placeholder = { 
            Text(
                text = placeholderString,
                fontSize = 14.sp,
                lineHeight = 14.sp
            ) 
        },
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.White,
            unfocusedContainerColor = Color.White,
            focusedIndicatorColor = Color.White,
            unfocusedIndicatorColor = Color.White,
        ),
        onValueChange = onValueChange,
        shape = RoundedCornerShape(8.dp)
    )
}

@Composable
fun EditTextField(
    value: String,
    onValueChange: (String) -> Unit,
    prefixStr: String,
    suffixStr: String,
    onClick: (() -> Unit)? = null
) {
    TextField(
        value = value,
        onValueChange = onValueChange,
        textStyle = TextStyle(
            color = Color.Gray,
            fontSize = 16.sp,
        ),
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.Transparent,
            unfocusedContainerColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent,
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
        ),
        modifier = Modifier
            .widthIn(min = 105.dp, max = 200.dp)
            .width(105.dp)
            .clickable(enabled = onClick != null) { onClick?.invoke() },
        singleLine = true,
        prefix = { Text(text = prefixStr, color = Color.Gray, fontSize = 16.sp) },
        suffix = { Text(text = suffixStr, color = Color.Gray, fontSize = 16.sp) },
    )
}


@Composable
fun EditBasicTextField(value: String,
                       onValueChange: (String) -> Unit,
                       keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
                       keyboardActions: KeyboardActions = KeyboardActions.Default,//键盘的选项,例如键盘类型、输入法
)
{

    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        textStyle = TextStyle(
            color = Color.Black,           // 字体颜色
            fontSize = 16.sp, // 字体大小
            textAlign = TextAlign.Right
        ),
        modifier = Modifier.width(150.dp),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = true,
    )

}

@Composable
fun EditBasicTextField1(
    value: String,
    onValueChange: (String) -> Unit,
    initialCursorPosition: Int = -1 // -1 表示默认位置（文本末尾）
) {
    // 将 String 转换为 TextFieldValue
    var textFieldValue by remember(value) {
        mutableStateOf(
            TextFieldValue(
                text = value,
                selection = TextRange(
                    if (initialCursorPosition >= 0) minOf(initialCursorPosition, value.length)
                    else value.length
                )
            )
        )
    }

    // 当外部 value 改变时更新 textFieldValue
    LaunchedEffect(value) {
        if (value != textFieldValue.text) {
            textFieldValue = textFieldValue.copy(
                text = value,
                selection = TextRange(
                    if (initialCursorPosition >= 0) minOf(initialCursorPosition, value.length)
                    else value.length
                )
            )
        }
    }

    BasicTextField(
        value = textFieldValue,
        onValueChange = { newValue ->
            textFieldValue = newValue
            onValueChange(newValue.text)
        },
        textStyle = TextStyle(
            color = Color.Gray,
            fontSize = 16.sp,
        ),
        modifier = Modifier
            .width(IntrinsicSize.Min)
            .onFocusEvent { focusState ->
                if (focusState.isFocused) {
                    // 获得焦点时设置光标位置
                    textFieldValue = textFieldValue.copy(
                        selection = TextRange(
                            if (initialCursorPosition >= 0)
                                minOf(initialCursorPosition, textFieldValue.text.length)
                            else textFieldValue.text.length
                        )
                    )
                }
            },
        singleLine = true,
    )
}