package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.LightBlue_FF
import com.ymx.photovoltaic.ui.page.theme.LightGreen_F0
import com.ymx.photovoltaic.ui.page.theme.LightOrange_DE
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.report_co2
import photovoltaic_kmp_app.composeapp.generated.resources.report_coal
import photovoltaic_kmp_app.composeapp.generated.resources.report_prompt
import photovoltaic_kmp_app.composeapp.generated.resources.report_tree
import kotlin.math.round

/**
 * 社会贡献组件
 * 
 * @param co2Reduction 减排二氧化碳量（单位：T）
 * @param coalSaving 节约标准煤量（单位：T）
 * @param treePlanting 等效植树数量（单位：棵）
 * @param modifier 可选的Modifier参数，可用于自定义组件的布局
 */
@Composable
fun SocialContributionWidget(
    co2Reduction: Double,
    coalSaving: Double,
    treePlanting: Double,
    modifier: Modifier = Modifier
) {
    // 添加对话框显示状态
    var showDialog by remember { mutableStateOf(false) }
    
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp, vertical = 6.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 标题行
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                TitleItem(text = "社会贡献")
                Spacer(modifier = Modifier.width(8.dp))

                Icon(
                    painter = painterResource(Res.drawable.report_prompt),
                    contentDescription = "社会贡献提示",
                    modifier = Modifier
                        .size(12.dp)
                        .clickable { showDialog = true },
                    tint = Color.Gray
                )
               
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 贡献数据行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 减排二氧化碳
                ContributionItemWithIcon(
                    value = formatToOneDecimal(co2Reduction),
                    unit = "T",
                    description = "减排二氧化碳",
                    icon = Res.drawable.report_co2,
                    backgroundColor = LightBlue_FF,
                    modifier = Modifier.weight(1f).padding(horizontal = 4.dp)
                )
                
                // 节约标准煤
                ContributionItemWithIcon(
                    value = formatToOneDecimal(coalSaving),
                    unit = "T",
                    description = "节约标准煤",
                    icon = Res.drawable.report_coal,
                    backgroundColor = LightOrange_DE,
                    modifier = Modifier.weight(1f).padding(horizontal = 4.dp)
                )
                
                // 等效植树
                ContributionItemWithIcon(
                    value = formatToOneDecimal(treePlanting),
                    unit = "棵",
                    description = "等效植树",
                    icon = Res.drawable.report_tree,
                    backgroundColor = LightGreen_F0,
                    modifier = Modifier.weight(1f).padding(horizontal = 4.dp)
                )
            }
        }
    }
    
    // 显示节能减排计算标准弹窗
    if (showDialog) {
        EnergyConsumptionDialog(
            onDismiss = { showDialog = false },
            co2Value = "0.997 kg",
            coalValue = "0.404 kg",
            treeValue = "0.054 棵"
        )
    }
}

/**
 * 格式化数字为一位小数
 */
private fun formatToOneDecimal(value: Double): String {
    val roundedValue = round(value * 10) / 10
    return if (roundedValue == roundedValue.toLong().toDouble()) {
        roundedValue.toLong().toString()
    } else {
        roundedValue.toString()
    }
}

/**
 * 带图标的贡献项目
 * 
 * @param value 贡献值
 * @param unit 单位
 * @param description 描述
 * @param icon 图标资源
 * @param backgroundColor 背景颜色
 * @param modifier 可选的Modifier参数，用于自定义布局
 */
@Composable
private fun ContributionItemWithIcon(
    value: String,
    unit: String,
    description: String,
    icon: DrawableResource,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .padding(horizontal = 8.dp, vertical = 8.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.Start
        ) {
        // 将数值和单位放在同一行
        Row(
            verticalAlignment = Alignment.Bottom,
        ) {
            Text(
                text = value,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
            )
            
            Spacer(modifier = Modifier.width(2.dp))
            
            Text(
                text = unit,
                style = TextStyle(
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            )
        }
        
        Spacer(modifier = Modifier.height(2.dp))
        
        // 将描述和图标放在同一行，图标在右侧
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = description,
                style = TextStyle(
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            )
            
            Image(
                painter = painterResource(icon),
                contentDescription = description,
                modifier = Modifier.width(30.dp).height(26.dp)
            )
        }
    }
    }
}
