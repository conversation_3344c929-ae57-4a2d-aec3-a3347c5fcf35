package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.ui.page.theme.Blue_F9
import com.ymx.photovoltaic.util.CommonUtil.roundTo2Decimals
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.viewmodel.StationViewModel
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.co2_format
import photovoltaic_kmp_app.composeapp.generated.resources.co2_reduction
import photovoltaic_kmp_app.composeapp.generated.resources.component_number_format
import photovoltaic_kmp_app.composeapp.generated.resources.component_status
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_operation
import photovoltaic_kmp_app.composeapp.generated.resources.group_belong
import photovoltaic_kmp_app.composeapp.generated.resources.group_operation
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.operation_success_refresh_later
import photovoltaic_kmp_app.composeapp.generated.resources.output_power
import photovoltaic_kmp_app.composeapp.generated.resources.output_voltage
import photovoltaic_kmp_app.composeapp.generated.resources.power_unit_format
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_off
import photovoltaic_kmp_app.composeapp.generated.resources.status_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temperature
import photovoltaic_kmp_app.composeapp.generated.resources.temperature_unit_format
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off_group
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on_group
import photovoltaic_kmp_app.composeapp.generated.resources.voltage_format


@OptIn(ExperimentalMaterial3Api::class)
@Composable
// 点击优化器，出现底部详细信息弹窗
fun ComponentInfoDialog(
    showDialog: Boolean,
    onDismiss: () -> Unit,
    onClick: () -> Unit,
    optimizer: StationView.InnerOptimizer,
    stationViewModel: StationViewModel? = null
) {
    var showGroupOperationDialog by remember { mutableStateOf(false) }
    var groupOperationFlag by remember { mutableStateOf(0) }
    
    // 获取字符串资源
    val operationFailedStr = stringResource(Res.string.operation_failed)
    val operationSuccessRefreshLaterStr = stringResource(Res.string.operation_success_refresh_later)

    if (showDialog) {
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            sheetState = rememberModalBottomSheetState(),
            dragHandle = null, // 移除顶部拖动条
        ) {
            Column(
                modifier = Modifier.background(Color.White)
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                       val chipId= if(optimizer.chipId.length>6) optimizer.chipId.substring(2,7) else optimizer.chipId
                        Text(
                            text = stringResource(Res.string.component_number_format).replace("%s", chipId),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Button(
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Blue_F9,
                                contentColor = Color.White
                            ),
                            onClick = { onClick()
                            }
                        ) {
                            if (optimizer.status==2)
                            Text(stringResource(Res.string.turn_on))
                            else
                            Text(stringResource(Res.string.turn_off))
                        }

                    }

                InfoItem(stringResource(Res.string.component_status), when(optimizer.status) {
                    1 -> stringResource(Res.string.status_normal)
                    2 -> stringResource(Res.string.status_off)
                    3 -> stringResource(Res.string.status_warning)
                    else -> stringResource(Res.string.status_normal)
                })
                InfoItem(
                    stringResource(Res.string.output_voltage),
                    if (optimizer.outputVoltage == -1) "" else stringResource(Res.string.voltage_format).replace("%s", (optimizer.outputVoltage/1000).roundTo2Decimals().toString())
                )
                InfoItem(
                    stringResource(Res.string.output_power),
                    if (optimizer.power == -1f) "" else stringResource(Res.string.power_unit_format).replace("%s", optimizer.power.toString())
                )
                InfoItem(
                    stringResource(Res.string.temperature),
                    if (optimizer.componentTemperature == -1) "" else stringResource(Res.string.temperature_unit_format).replace("%d", optimizer.componentTemperature.toString())
                )
                InfoItem(
                    stringResource(Res.string.co2_reduction),
                    if (optimizer.power == -1f) "" else stringResource(Res.string.co2_format).replace("%s", (optimizer.power*0.997).roundTo2Decimals().toString())
                )
                
                // 组串信息和操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(Res.string.group_belong),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = optimizer.groupName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                // 添加组串操作行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(Res.string.group_operation),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    // 根据组串状态显示不同的按钮
                    when (optimizer.groupStatus) {
                        2 -> { // 组串全部关闭
                            Button(
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Blue_F9,
                                    contentColor = Color.White
                                ),
                                onClick = { 
                                    groupOperationFlag = 2 // 打开
                                    showGroupOperationDialog = true
                                }
                            ) {
                                Text(stringResource(Res.string.turn_on_group))
                            }
                        }
                        1 -> { // 组串全部打开
                            Button(
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Blue_F9,
                                    contentColor = Color.White
                                ),
                                onClick = { 
                                    groupOperationFlag = 1 // 关闭
                                    showGroupOperationDialog = true
                                }
                            ) {
                                Text(stringResource(Res.string.turn_off_group))
                            }
                        }
                        3 -> { // 组串状态不统一
                            Row {
                                Button(
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Blue_F9,
                                        contentColor = Color.White
                                    ),
                                    onClick = { 
                                        groupOperationFlag = 2 // 打开
                                        showGroupOperationDialog = true
                                    }
                                ) {
                                    Text(stringResource(Res.string.turn_on_group))
                                }
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Button(
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Blue_F9,
                                        contentColor = Color.White
                                    ),
                                    onClick = { 
                                        groupOperationFlag = 1 // 关闭
                                        showGroupOperationDialog = true
                                    }
                                ) {
                                    Text(stringResource(Res.string.turn_off_group))
                                }
                            }
                        }
                    }
                }
                
                HorizontalDivider(thickness = 1.dp, color = Color.LightGray) // 添加分割线
            }
        }
        
        // 组串操作确认对话框
        if (showGroupOperationDialog && stationViewModel != null) {
            val operationText = if (groupOperationFlag == 2) 
                stringResource(Res.string.turn_on_group) 
            else 
                stringResource(Res.string.turn_off_group)
                
            CustomDialog(
                confirmStr = stringResource(
                    Res.string.confirm_operation
                ).replace("%1\$s", operationText).replace("%2\$s", optimizer.groupName),
                onConfirm = {
                    showGroupOperationDialog = false
                    
                    stationViewModel.remoteControllerAck(
                        powerStationId = null,
                        groupId = optimizer.groupId,
                        chipId = null,
                        flag = groupOperationFlag,
                        errorBlock = {
                            ToastUtil.showLong(operationFailedStr)
                        }
                    ) {
                        ToastUtil.showLong(operationSuccessRefreshLaterStr)
                    }
                },
                onCancel = {
                    showGroupOperationDialog = false
                }
            )
        }
    }
}

@Composable
 fun InfoItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 10.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
    HorizontalDivider(thickness = 1.dp, color = Color.LightGray) // 添加分割线
}

