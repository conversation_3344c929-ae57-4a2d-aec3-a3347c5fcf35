package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.ComponentItem
import com.ymx.photovoltaic.ui.page.widget.CustomMenuItem
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.edit_group
import photovoltaic_kmp_app.composeapp.generated.resources.group_management
import photovoltaic_kmp_app.composeapp.generated.resources.input_group_name
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_count


@Composable
fun GroupListPage(
    navHostController: NavHostController,
    collectorId: String,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.group_management),
                backClick = { navHostController.popBackStack() })
        },
        backgroundColor = Color(242, 243, 245, 1),
        floatingActionButton = {
            FloatingButton{ 
                navHostController.navigate(Route.GROUP_NEW+"/$collectorId")
            }
        }
    ) { paddingValues ->
        GroupContent(
            navHostController = navHostController,
            collectorId = collectorId,
            equipmentViewModel = equipmentViewModel,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

@Composable
fun GroupContent(
    navHostController: NavHostController,
    collectorId: String,
    equipmentViewModel: EquipmentViewModel,
    modifier: Modifier = Modifier
) {
    val groupList by equipmentViewModel.groupListFlow.collectAsState()
    var searchQuery by remember { mutableStateOf("") }

    val filteredGroups = groupList.filter {
        it.groupName.contains(searchQuery, ignoreCase = true)
    }

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchGroupList(collectorId)
    }

    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(5.dp))
        CommonSearch(
            searchQuery = searchQuery,
            onQueryChange = { searchQuery = it },
            placeholderTextResId = Res.string.input_group_name,
            Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(55.dp)
                .clip(RoundedCornerShape(25.dp))
        )

        val newGroupList = filteredGroups ?: emptyList()

        LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
            items(newGroupList) { group ->
                val customMenuItems = listOf(
                    CustomMenuItem(stringResource(Res.string.edit_group)) {
                        navHostController.navigate(
                            Route.GROUP_EDIT + "/${group.id}/${group.groupName}/${group.power}"
                        )
                    }
                )
                ComponentItem(
                    "group",
                    group.groupName,
                    stringResource(Res.string.optimizer_count) + group.groupNum,
                    menuItems = customMenuItems,
                    onClick = {
                        navHostController.navigate(Route.OPTIMIZER+"/${group.id}")
                    }
                )
            }
        }
    }
}



