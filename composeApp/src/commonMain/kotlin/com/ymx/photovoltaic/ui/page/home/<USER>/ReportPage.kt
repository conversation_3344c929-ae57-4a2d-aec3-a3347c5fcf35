package com.ymx.photovoltaic.ui.page.home.report


import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.SearchGroup
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CustomLineChart
import com.ymx.photovoltaic.ui.page.widget.DateType
import com.ymx.photovoltaic.ui.page.widget.HandleColumnData
import com.ymx.photovoltaic.ui.page.widget.PickerInput
import com.ymx.photovoltaic.ui.page.widget.PowerStatisticsCard
import com.ymx.photovoltaic.ui.page.widget.PowerStatsCard
import com.ymx.photovoltaic.ui.page.widget.SocialContributionWidget
import com.ymx.photovoltaic.ui.page.widget.TabMenu
import com.ymx.photovoltaic.ui.page.widget.TitleItem
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.WeatherStatusBar
import com.ymx.photovoltaic.ui.page.widget.rememberCustomDatePickerState
import com.ymx.photovoltaic.util.CommonUtil.roundTo2Decimals
import com.ymx.photovoltaic.util.CommonUtil.roundTo3Decimals
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.viewmodel.ReportViewModel
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.all
import photovoltaic_kmp_app.composeapp.generated.resources.data_statistics
import photovoltaic_kmp_app.composeapp.generated.resources.day
import photovoltaic_kmp_app.composeapp.generated.resources.month
import photovoltaic_kmp_app.composeapp.generated.resources.power_station_report
import photovoltaic_kmp_app.composeapp.generated.resources.unit_kw
import photovoltaic_kmp_app.composeapp.generated.resources.year


@Composable
 fun ReportPage(navHostController: NavHostController,
                       reportViewModel: ReportViewModel = getKoin().get()
)
{
    val powerDayData by reportViewModel.powerDayFlow.collectAsState()
    val groupData by reportViewModel.groupFlow.collectAsState()
    val tendMonthData by reportViewModel.tendMonthFlow.collectAsState()
    val tendYearData by reportViewModel.tendYearFlow.collectAsState()
    val tendAllData by reportViewModel.tendAllFlow.collectAsState()
    val tendMinuteData by reportViewModel.tendMinuteFlow.collectAsState()
    val tendMinuteGroupData by reportViewModel.tendMinuteGroupFlow.collectAsState()
    val weatherData by reportViewModel.weatherFlow.collectAsState()
    val historyKwhData by reportViewModel.historyKwhFlow.collectAsState()

    val nowDay = DateTimeUtil.now()
    val year = nowDay.year
    val month = DateTimeUtil.formatYearMonth(nowDay)
    val startDate = DateTimeUtil.fromEpochMillis(AppGlobal.powCreateTime)
    val day=stringResource(Res.string.day)
    var selectStr by remember { mutableStateOf(day) }

    var groupList= mutableListOf<SearchGroup>()

    val unit_kw=stringResource(Res.string.unit_kw)

    var title by remember { mutableStateOf(unit_kw) }

    var selectDay by remember { mutableStateOf(DateTimeUtil.now()) }

    var selectMonth by remember { mutableStateOf(DateTimeUtil.now()) }
    var selectYear by remember { mutableStateOf(DateTimeUtil.now()) }
    val state = rememberCustomDatePickerState()

    LaunchedEffect(selectDay)
    {
        reportViewModel.queryReportByMinute(AppGlobal.powerStationId,selectDay.toString())
        // 如果选择的不是今天，调用历史电量接口
        if (selectDay.toString() != nowDay.toString()) {
            reportViewModel.queryHistoryKwh(AppGlobal.powerStationId, selectDay.toString())
        }
        
        // 如果选择的月份不是当前月份，查询历史月份数据
        if (DateTimeUtil.formatYearMonth(selectDay) != DateTimeUtil.formatYearMonth(nowDay)) {
            reportViewModel.queryReportByDate(AppGlobal.powerStationId, DateTimeUtil.formatYearMonth(selectDay), "day")
        }
        
        // 如果选择的年份不是当前年份，查询历史年份数据
        if (selectDay.year != nowDay.year) {
            reportViewModel.queryReportByDate(AppGlobal.powerStationId, selectDay.year.toString(), "month")
        }
    }

    LaunchedEffect(selectMonth)
    {
        reportViewModel.queryReportByDate(AppGlobal.powerStationId,DateTimeUtil.formatYearMonth(selectMonth),"day")
        if(DateTimeUtil.formatYearMonth(selectMonth).equals(DateTimeUtil.formatYearMonth(nowDay)))
        {
            reportViewModel.queryTotalReport(AppGlobal.powerStationId)
        }
    }

    LaunchedEffect(selectYear)
    {
        reportViewModel.queryReportByDate(AppGlobal.powerStationId,selectYear.year.toString(),"month")
    }

    LaunchedEffect (Unit){
        reportViewModel.queryTotalReport(AppGlobal.powerStationId)
        reportViewModel.queryReportByMinute(AppGlobal.powerStationId, nowDay.toString())

         delay(500)
        reportViewModel.getWeather(AppGlobal.powerDistrictId, "now")
        delay(500)
        reportViewModel.queryReportByDate(AppGlobal.powerStationId,month,"day")
        reportViewModel.queryReportByDate(AppGlobal.powerStationId,year.toString(),"month")
        reportViewModel.queryReportByDate(AppGlobal.powerStationId,"","year")
        //  reportViewModel.queryGroup(AppGlobal.powerStationId,"")
//        reportViewModel.queryReportByMinuteGroup(AppGlobal.powerStationId,selectDay.toString(),
//            "","")

    }

    LaunchedEffect(groupData)
    {
        groupData.let {
            groupList = it.toMutableList()
        }
    }

    // 当前功率 w
    val nowPower=powerDayData?.power?:0f
    // 今天到现在为止的功率之和 w
    val todayPower=powerDayData?.powerSum?:0f
    // 当日发电量 kwh - 如果是历史日期且有历史数据，使用历史数据；否则使用当前数据
    val todayKwh = if (selectDay.toString() != nowDay.toString() && historyKwhData != null) {
        historyKwhData!!.kwh.roundTo2Decimals()
    } else {
        (powerDayData?.dayKwh ?: 0f).roundTo2Decimals()
    }
    // 累计发电量 kwh
    var sumKwh=(powerDayData?.kwh?:0f).roundTo2Decimals()

    // 计算当月发电量：如果是当前月份，加上当日发电量；如果是历史月份，直接使用历史数据


    val monthKwh = if (selectDay.toString() == nowDay.toString()) {
        ((tendMonthData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f) + todayKwh).roundTo2Decimals()
    } else if (DateTimeUtil.formatYearMonth(selectDay) == DateTimeUtil.formatYearMonth(nowDay)){
        ((tendMonthData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f) + (powerDayData?.dayKwh ?: 0f)).roundTo2Decimals()
    }
    else {
        (tendMonthData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f).roundTo2Decimals()
    }

    // 计算当年发电量：如果是当前年份，加上当日发电量；如果是历史年份，直接使用历史数据
    val yearKwh = if (selectDay.toString() == nowDay.toString()) {
        ((tendYearData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f) + todayKwh).roundTo2Decimals()
    } else if (selectDay.year == nowDay.year){
        ((tendYearData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f) + (powerDayData?.dayKwh ?: 0f)).roundTo2Decimals()
    }
    else {
        (tendYearData?.data?.sumOf { it.kwh.toDouble() }?.toFloat() ?: 0f).roundTo2Decimals()
    }

    // 累计发电量和当年发电量算法不同，有一些情况下四舍五入存在误差，进行修正
    if(sumKwh<yearKwh)
    {
        sumKwh=yearKwh
    }

    // 当日收益和累计收益（发电量乘以0.3元/度）
    val todayIncome = (todayKwh * 0.3f).roundTo2Decimals()
    val monthIncome = (monthKwh * 0.3f).roundTo2Decimals()
    val yearIncome = (yearKwh * 0.3f).roundTo2Decimals()
    val totalIncome = (sumKwh * 0.3f).roundTo2Decimals()

    // 减排co2总量 T
    val totalReduceCO2Num = (sumKwh * 0.997/1000f).roundTo3Decimals()
    // 节约标准每总量 T
    val totalReduceCoalNum = (sumKwh * 0.404/1000f).roundTo3Decimals()

    val totalTreePlantingNum = (sumKwh * 0.054).roundTo2Decimals()


    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.power_station_report),
                backClick = { navHostController.navigate(Route.HOME)}
            )
        },
        containerColor = Grey_F5,
        bottomBar = {
            CommonBottomBar(navController = navHostController, isFirstMenu = false)
        }
    ) {
        paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues).verticalScroll(rememberScrollState())
        ) {
            // 天气预报
            weatherData?.let { w ->
                WeatherStatusBar(
                    location = AppGlobal.powerDistrictId,
                    temperature = "${w.temp}°C",
                    weather = w.text,
                    sunTime = "日照 ${AppGlobal.sunUpTime}-${AppGlobal.sunDownTime}",
                    powerStatus = AppGlobal.powerStatus
                )
            }

            // 电力数据
            TopDataPart(nowPower, todayPower, todayKwh, monthKwh, yearKwh, sumKwh, 
                        todayIncome, monthIncome, yearIncome, totalIncome)

            Spacer(modifier = Modifier.height(5.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth().padding(horizontal = 10.dp).clip(RoundedCornerShape(16.dp))
                    .background(Color.White)
                    .padding(12.dp)
            ) {
                TitleItem(text = stringResource(Res.string.data_statistics),
                    textColor = Color.Black)

                Spacer(modifier = Modifier.height(5.dp))

                Column {
                    // 第一行：日月年全部滑动菜单
                    TabMenu { selectOne ->
                        run {
                            selectStr = selectOne
                        }
                    }

                    if(selectStr != stringResource(Res.string.all))
                    {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.Center,
                        ) {
                            when(selectStr)
                            {
                                stringResource(Res.string.day)-> {
                                    PickerInput(
                                        selectedDate = selectDay.toString(),
                                        onClickDo = {
                                            state.show(selectDay, start = startDate, end=nowDay) {
                                                selectDay = it
                                            }
                                        },
                                        onPreviousDate = {
                                            val prevDay = DateTimeUtil.minusDays(selectDay, 1)
                                            if (prevDay.compareTo(startDate) >= 0) {
                                                selectDay = prevDay
                                            }
                                        },
                                        onNextDate = {
                                            val nextDay = DateTimeUtil.plusDays(selectDay, 1)
                                            if (nextDay.compareTo(nowDay) <= 0) {
                                                selectDay = nextDay
                                            }
                                        }
                                    )
                                }
                                stringResource(Res.string.month)-> {
                                    PickerInput(
                                        selectedDate = selectMonth.toString().substring(0, 7),
                                        onClickDo = {
                                            state.show(selectMonth,start = startDate,end=nowDay,type = DateType.MONTH) {
                                                selectMonth = it
                                            }
                                        },
                                        onPreviousDate = {
                                            val prevMonth = DateTimeUtil.minusMonths(selectMonth, 1)
                                            if (prevMonth.compareTo(startDate) >= 0) {
                                                selectMonth = prevMonth
                                            }
                                        },
                                        onNextDate = {
                                            val nextMonth = DateTimeUtil.plusMonths(selectMonth, 1)
                                            if (nextMonth.compareTo(nowDay) <= 0) {
                                                selectMonth = nextMonth
                                            }
                                        }
                                    )
                                }
                                stringResource(Res.string.year)-> {
                                    PickerInput(
                                        selectedDate = selectYear.toString().substring(0, 4),
                                        onClickDo = {
                                            state.show(selectYear,start = startDate,end=nowDay,type = DateType.YEAR) {
                                                selectYear = it
                                            }
                                        },
                                        onPreviousDate = {
                                            val prevYear = DateTimeUtil.minusYears(selectYear, 1)
                                            if (prevYear.compareTo(startDate) >= 0) {
                                                selectYear = prevYear
                                            }
                                        },
                                        onNextDate = {
                                            val nextYear = DateTimeUtil.plusYears(selectYear, 1)
                                            if (nextYear.compareTo(nowDay) <= 0) {
                                                selectYear = nextYear
                                            }
                                        }
                                    )
                                }
                            }
                        }

                    }

                    Text(text = title,modifier = Modifier.padding(start = 5.dp))

                }
                // xList改变，producer producer 改变 图表重组
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(170.dp)
                ){

                    when(selectStr)
                    {
                        stringResource(Res.string.day)-> {
                            CustomLineChart(tendMinuteData)
                            title = "kw"
                        }

                        stringResource(Res.string.month)-> {
                            HandleColumnData(tendMonthData, selectStr)
                            title = "kwh"
                        }
                        stringResource(Res.string.year)->
                        {
                            HandleColumnData(tendYearData, selectStr)
                            title = "kwh"
                        }
                        stringResource(Res.string.all)-> {
                            HandleColumnData(tendAllData, selectStr)
                            title = "kwh"
                        }

                    }
                }

            }


            Spacer(modifier = Modifier.height(5.dp))
            // 第三部分：社会贡献
            BottomDataPart(totalReduceCO2Num,totalReduceCoalNum,totalTreePlantingNum)

            Spacer(modifier = Modifier.height(2.dp))
        }

    }


}

@Composable
fun TopDataPart(
    nowPower: Float, 
    todayPower: Float, 
    todayKwh: Float, 
    monthKwh: Float,
    yearKwh: Float,
    sumKwh: Float, 
    todayIncome: Float, 
    monthIncome: Float,
    yearIncome: Float,
    totalIncome: Float
) {
    Column(
        modifier = Modifier.padding(vertical = 2.dp)
    ) {
        // 使用PowerStatisticsCard组件显示收益和发电量统计
        PowerStatisticsCard(
            dayIncome = todayIncome.toString(),
            monthIncome = monthIncome.toString(),  // 当月收益
            yearIncome = yearIncome.toString(),    // 当年收益
            totalIncome = totalIncome.toString(),
            dayPower = todayKwh.toString(),
            monthPower = monthKwh.toString(),   // 当月发电量
            yearPower = yearKwh.toString(),     // 当年发电量
            totalPower = sumKwh.toString()
        )
        
        Spacer(modifier = Modifier.height(3.dp))
        
        // 添加功率状态卡片
        PowerStatsCard(
            realTimePower = (nowPower/1000).roundTo2Decimals().toString(),
            realTimePowerUnit = "kW",
            powerRatio = (nowPower/10/AppGlobal.stationPower).roundTo2Decimals().toString(),  // 功率比例数据暂无，使用0
            powerRatioUnit = "%",
            installedPower = AppGlobal.stationPower.toString(),
            installedPowerUnit = "kWp"
        )
    }
}

@Composable
fun BottomDataPart(totalReduceCO2Num:Float,totalReduceCoalNum:Float,totalTreePlantingNum:Float) {
    // 使用SocialContributionWidget组件显示社会贡献数据
    SocialContributionWidget(
        co2Reduction = totalReduceCO2Num.toDouble(),
        coalSaving = totalReduceCoalNum.toDouble(),
        treePlanting = totalTreePlantingNum.toDouble()
    )
}



