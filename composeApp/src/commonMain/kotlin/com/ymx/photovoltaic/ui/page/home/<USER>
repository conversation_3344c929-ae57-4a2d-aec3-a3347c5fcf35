package com.ymx.photovoltaic.ui.page.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Card
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.ContentDialog
import com.ymx.photovoltaic.ui.page.widget.DeviceStatsCard
import com.ymx.photovoltaic.ui.page.widget.HeaderBar
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import com.ymx.photovoltaic.ui.page.widget.StationItem
import com.ymx.photovoltaic.util.CommonUtil.getDaysSinceTime
import com.ymx.photovoltaic.viewmodel.HomeViewModel
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add_device
import photovoltaic_kmp_app.composeapp.generated.resources.home_title
import photovoltaic_kmp_app.composeapp.generated.resources.no_warnings
import photovoltaic_kmp_app.composeapp.generated.resources.power_warning_count
import photovoltaic_kmp_app.composeapp.generated.resources.reminder
import photovoltaic_kmp_app.composeapp.generated.resources.station1
import photovoltaic_kmp_app.composeapp.generated.resources.station2
import photovoltaic_kmp_app.composeapp.generated.resources.station3
import photovoltaic_kmp_app.composeapp.generated.resources.station4
import photovoltaic_kmp_app.composeapp.generated.resources.station_add
import photovoltaic_kmp_app.composeapp.generated.resources.status_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_offline
import photovoltaic_kmp_app.composeapp.generated.resources.temp_shutdown_count
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning_count
import photovoltaic_kmp_app.composeapp.generated.resources.warning_notification

// 添加一个伴生对象来保存是否已经显示过弹窗的状态
object HomePageState {
    var hasShownWarningDialog = false
}

@Composable
fun HomePage(
    navHostController: NavHostController,
    homeViewModel: HomeViewModel = getKoin().get()
) {

    SetStatusBar(Color.White,true)


    val stationList by homeViewModel.stationListFlow.collectAsState()
    val warningTypeCount by homeViewModel.warningTypeCountFlow.collectAsState()
    val weatherMap by homeViewModel.weatherFlow.collectAsState()
    
    // 添加显示弹窗的状态
    var showWarningDialog by remember { mutableStateOf(false) }

    // 获取当前位置和温度信息
    var location by remember { mutableStateOf("嘉兴") }  // 使用 mutableStateOf 使位置可变
    var temperature by remember { mutableStateOf("--°C") }  // 默认温度显示
    
    // 提取所有电站的 districtId
    val districtIds = stationList.map { it.districtId }.distinct()

    // 更新当前位置对应的温度
    LaunchedEffect(location, weatherMap) {
        // 根据当前选择的位置更新温度显示
        weatherMap[location]?.let {
            temperature = "${it.text} ${it.temp}°C"
        }
    }

    LaunchedEffect(Unit) {
        // 检查 AppGlobal.mId 是否为空
        if (AppGlobal.mId.isEmpty()) {
            // 如果为空，尝试从 CacheManager 获取
            val cachedMid = CacheManager.getMid()
            if (!cachedMid.isNullOrEmpty()) {
                AppGlobal.mId = cachedMid
                homeViewModel.fetchStationList(AppGlobal.mId, "2")
            }
        } else {
            homeViewModel.fetchStationList(AppGlobal.mId, "2")
        }
    }

    // 获取电站ID列表并调用新接口
    LaunchedEffect(stationList) {
        val powerIdList = stationList.map { it.id }
        if (powerIdList.isNotEmpty()) {
            AppGlobal.powerIdList = powerIdList
            homeViewModel.fetchWarningTypeCount(powerIdList)
            delay(200)
            
            // 获取当前语言
            val language = CacheManager.getLanguage()
            val apiLanguage = if (language == "zh") "cn" else "en"
            
            if (!RegionDataManager.isInitialized()) {
                homeViewModel.fetchRegions(apiLanguage)
            }
            
            // 获取不重复的地区ID列表的天气
            val uniqueDistrictIds = stationList.map { it.districtId }.distinct()
            if (uniqueDistrictIds.isNotEmpty()) {
                homeViewModel.getMultipleWeather(uniqueDistrictIds)
                // 如果location在uniqueDistrictIds中存在，则使用它，否则使用第一个ID
                if (uniqueDistrictIds.contains(location)) {
                    homeViewModel.getWeather(location, "now")
                } else if (uniqueDistrictIds.isNotEmpty()) {
                    location = uniqueDistrictIds.first()
                    homeViewModel.getWeather(location, "now")
                }
            }
        }
    }
    
    // 当warningTypeCount变化且不为空时，只有在第一次进入首页时才显示弹窗
    LaunchedEffect(warningTypeCount) {
        if (!HomePageState.hasShownWarningDialog && 
            warningTypeCount != null && 
            warningTypeCount?.typeStats?.isNotEmpty() == true) {
            showWarningDialog = true
            HomePageState.hasShownWarningDialog = true
        }
    }

    var searchQuery by remember { mutableStateOf("") }

    // 添加状态来跟踪当前选中的状态
    var selectedStatus by remember { mutableStateOf("all") }
    


    // 计算异常电站数量
    val exceptionCount = warningTypeCount?.stationIds?.size ?: 0

    val offlineCount = stationList.filter { it.status == 1 }.size

    // 获取异常电站ID列表
    val exceptionStationIds = warningTypeCount?.stationIds ?: emptyList()

    val filteredItems = stationList.filter {
        // 首先按状态过滤
        val matchesStatus = when (selectedStatus) {
            "all" -> true
            "normal" -> it.status != 1 && !exceptionStationIds.contains(it.id)
            "abnormal" -> exceptionStationIds.contains(it.id)
            "offline" -> it.status == 1
            else -> true
        }

        // 在状态过滤的基础上进行搜索
        matchesStatus && (
                searchQuery.isEmpty() ||
                        it.systemName.contains(searchQuery, ignoreCase = true)
                )
    }


    // 显示警告弹窗
    if (showWarningDialog) {
        ContentDialog(
            content = {
                WarningContent(warningTypeCount = warningTypeCount?.typeStats,
                    onClick = { navHostController.navigate(Route.MESSAGE) }
                    )
            },
            onConfirm = {
                showWarningDialog = false
            }
        )
    }

    Scaffold(
        topBar = {
            HeaderBar(
                title = stringResource(Res.string.home_title),
                temperature = temperature,
                location = location,
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onAddClick = { navHostController.navigate(Route.STATION_NEW) },
                onAlarmClick = { navHostController.navigate(Route.MESSAGE) },
                districtIds = districtIds,
                onLocationChange = { newLocation -> 
                    location = newLocation
                    // 当位置改变时，更新温度显示
                    weatherMap[newLocation]?.let {
                        temperature = "${it.temp}°C"
                    } ?: run {
                        // 如果没有该地区的天气信息，则获取它
                        homeViewModel.getWeather(newLocation, "now")
                    }
                }
            )
        },
        bottomBar = {
            CommonBottomBar(
                navController = navHostController
            )
        },
        containerColor = Grey_F5,
        floatingActionButtonPosition = FabPosition.Center,
        floatingActionButton = {
            // 根据用户类型决定是否显示浮动按钮
            val userType = CacheManager.getUser()?.type ?: 0
            if (userType != 1) {
                FloatingActionButton(
                    onClick = { navHostController.navigate(Route.STATION_NEW)},
                    containerColor = Color.Transparent,
                    contentColor = Color.Transparent,
                    elevation = FloatingActionButtonDefaults.elevation(0.dp, 0.dp, 0.dp, 0.dp),
                ) {
                    Image(
                        painter = painterResource(Res.drawable.station_add),
                        modifier = Modifier.size(60.dp),
                        contentDescription = stringResource(Res.string.add_device)
                    )
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier.padding(paddingValues)
        ) {
            // 使用DeviceStatsCard替换原来的PowerStatus
            DeviceStatsCard(
                totalCount = stationList.size,
                normalCount = stationList.size.minus(exceptionCount).minus(offlineCount),
                faultCount = exceptionCount,
                offlineCount = offlineCount,
//                modifier = Modifier.padding(top = 8.dp)
            )

            Spacer(modifier = Modifier.height(6.dp))

            // 提醒列表
            ReminderListScreen(
                warningTypeCount = warningTypeCount?.typeStats,
                onClick = { navHostController.navigate(Route.MESSAGE) }
            )

            Spacer(modifier = Modifier.height(6.dp))

            PowerStationList(
                stationList = filteredItems ?: emptyList(),
                navHostController = navHostController,
                page = Route.HOME,
                exceptionStationIds = exceptionStationIds,
                from = "home"
            )
        }
    }
}


@Composable
fun PowerStationList(
    stationList: List<Station>,
    navHostController: NavHostController,
    page: String,
    exceptionStationIds: List<String> = emptyList(),
    from: String = ""
) {
    val stationDrawables = listOf(
        Res.drawable.station1,
        Res.drawable.station2,
        Res.drawable.station3,
        Res.drawable.station4
    )

    LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
        itemsIndexed(stationList) { index, station ->
            // 在设备列表界面，没有请求警报信息，不显示状态
            val status =
                // 根据电站状态判断状态 status为1 表示未调试 离线状态
                if(station.status==1)
                {
                    stringResource(Res.string.status_offline)
                }
                else if (exceptionStationIds.isEmpty()) {
                    stringResource(Res.string.status_normal)
                } else if (exceptionStationIds.contains(station.id)) {
                    stringResource(Res.string.status_abnormal)
                } else {
                    stringResource(Res.string.status_normal)
                }

            val drawableRes = stationDrawables[index % stationDrawables.size]
            val address = station.districtId + " " + station.streetName
            val days = getDaysSinceTime(station.createTime)
            val createTime = station.createTimeCh.substring(startIndex = 0, endIndex = 10)


            StationItem(
                Color.White,
                drawableRes,
                station.systemName,
                address,
                station.power,
                createTime,
                days,
                status
            ) {
                when (page) {
                    Route.HOME -> {
                        // 传递status文本值
                        navHostController.navigate(
                            Route.REPORT+ "/${station.id}/${station.sunuptime}/${station.sundowntime}/${station.createTime}/${station.districtId}/${station.systemName}/$status/${station.power}"
                        )
                    }
                    Route.EQUIPMENT -> navHostController.navigate(
                        Route.DEVICE_CONFIG + "/${station.id}/${station.systemName}/$address/${station.power}/$createTime/$days/$status/$index"
                    )
                }
            }
        }
    }
}





@Composable
fun ReminderListScreen(
    warningTypeCount: Map<String, Int>?,
    onClick: () -> Unit
) {
    // 渐变背景颜色
    val gradientBackground = Brush.verticalGradient(
        colors = listOf(
             // 渐变开始颜色
            Color(255,202,156,255),
            Color(0xFFFFFF),// 完全透明的白色 - 渐变结束颜色
        ),
        startY = 0f,
        endY = Float.POSITIVE_INFINITY
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth() // 调小高度
            .padding(start = 10.dp, end = 10.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(24.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(gradientBackground)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 6.dp, bottom = 6.dp, start = 12.dp, end = 12.dp)  // 调整内边距
            ) {
                if (warningTypeCount.isNullOrEmpty()) {
                    ReminderItem(message = stringResource(Res.string.no_warnings))
                } else {
                    // 从 warningTypeCount 中获取各类型警告数量
                    val tempWarnNum = warningTypeCount["1"] ?: 0
                    val electWarnNum = warningTypeCount["2"] ?: 0
                    val tempOffNum = warningTypeCount["6"] ?: 0

                    if (tempWarnNum == 0 && electWarnNum == 0 && tempOffNum == 0) {
                        ReminderItem(message = stringResource(Res.string.no_warnings))
                    }

                    if (tempWarnNum > 0) {
                        ReminderItem(message = stringResource(Res.string.temp_warning_count).replace("%d", tempWarnNum.toString()))
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                    if (electWarnNum > 0) {
                        ReminderItem(message = stringResource(Res.string.power_warning_count).replace("%d", electWarnNum.toString()))
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                    if (tempOffNum > 0) {
                        ReminderItem(message = stringResource(Res.string.temp_shutdown_count).replace("%d", tempOffNum.toString()))
                    }
                }
            }
        }
    }
}

@Composable
fun ReminderItem(message: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // "提醒" 标识
        Box(
            modifier = Modifier
                .border(1.dp, Color(0xFFFFA500), RoundedCornerShape(20.dp))  // 橙色边框
                .padding(horizontal = 10.dp, vertical = 4.dp)
        ) {
            Text(
                text = stringResource(Res.string.reminder),
                color = Color(0xFFFFA500),
                fontSize = 13.sp
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        // 提醒内容
        Text(
            text = message,
            style = TextStyle(fontSize = 14.sp, color = Color.Gray),  // 改为黑色，更好地在渐变背景上显示
            modifier = Modifier.weight(1f)
        )

        // 向右箭头
        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = "Arrow",
            tint = Color.Gray  // 改为黑色
        )
    }
}

@Composable
fun WarningContent(warningTypeCount: Map<String, Int>?,
                   onClick: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .background(Color.White)
            .padding(16.dp)
    ) {
        if (!warningTypeCount.isNullOrEmpty()) {
            Text(
                text = stringResource(Res.string.warning_notification),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top=16.dp,bottom = 16.dp),
                textAlign = TextAlign.Center
            )
                // 从 warningTypeCount 中获取各类型警告数量
                val tempWarnNum = warningTypeCount["1"] ?: 0
                val electWarnNum = warningTypeCount["2"] ?: 0
                val tempOffNum = warningTypeCount["6"] ?: 0

            Column(modifier = Modifier.clickable { onClick() })
            {
                if (tempWarnNum == 0 && electWarnNum == 0 && tempOffNum == 0) {
                    ReminderItem(message = stringResource(Res.string.no_warnings))
                    return
                }

                if (tempWarnNum > 0) {
                    ReminderItem(message = stringResource(Res.string.temp_warning_count).replace("%d", tempWarnNum.toString()))
                    Spacer(modifier = Modifier.height(4.dp))
                }
                if (electWarnNum > 0) {
                    ReminderItem(message = stringResource(Res.string.power_warning_count).replace("%d", electWarnNum.toString()))
                    Spacer(modifier = Modifier.height(4.dp))
                }
                if (tempOffNum > 0) {
                    ReminderItem(message = stringResource(Res.string.temp_shutdown_count).replace("%d", tempOffNum.toString()))
                }
            }

            }
    }
}
