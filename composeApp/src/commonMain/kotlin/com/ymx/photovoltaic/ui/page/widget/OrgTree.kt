package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ymx.photovoltaic.data.bean.Inverter
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.home.station.ShowTurnOffComponentDialog
import com.ymx.photovoltaic.ui.page.home.station.calculateDisplayValue
import com.ymx.photovoltaic.util.CommonUtil.roundTo2Decimals
import com.ymx.photovoltaic.viewmodel.StationViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_off_groups
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_on_groups
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.operation_success_refresh_later
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_inverter
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_100
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_20
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_40
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_60
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_80
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_offline
import photovoltaic_kmp_app.composeapp.generated.resources.status_closed
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_warning

@Composable
fun OrgTreeScreen(
    dataSource: List<DeviceNode<DeviceNodeData>>,
    scale: Float,
    offsetX: Float,
    offsetY: Float,
    stationViewModel: StationViewModel,
    selectedOptimizerOption: String,
    onTransformGesture: (centroid: Offset, pan: Offset, zoom: Float, rotation: Float) -> Unit
) {
    // 计算树形结构的最大尺寸
    // 递归计算树的最大深度和宽度
    fun calculateTreeDimensions(nodes: List<DeviceNode<DeviceNodeData>>): Pair<Int, Int> {
        if (nodes.isEmpty()) return Pair(0, 0)
        
        var maxWidth = 0
        var maxDepth = 0
        
        nodes.forEach { node ->
            val (childWidth, childDepth) = calculateTreeDimensions(node.children)
            maxWidth = maxOf(maxWidth, if (node.children.size <= 1) 1 + childWidth else node.children.size + childWidth)
            maxDepth = maxOf(maxDepth, 1 + childDepth)
        }
        
        return Pair(maxWidth, maxDepth)
    }
    
    val (treeWidth, treeDepth) = calculateTreeDimensions(dataSource)
    
    // 基于spacing=20dp和节点大小计算Box尺寸
    val spacing = 20.dp
    val nodeMaxWidth = 80.dp  // 最大节点宽度
    val nodeMaxHeight = 80.dp // 最大节点高度
    
    // 计算总宽度：节点数量 * (节点宽度 + 间距) + 额外边距
    val calculatedWidth = (treeWidth * (nodeMaxWidth.value + spacing.value * 2) + 200).dp
    // 计算总高度：深度 * (节点高度 + 间距) + 额外边距  
    val calculatedHeight = (treeDepth * (nodeMaxHeight.value + spacing.value) + 200).dp
    
    // 打印计算出的尺寸
//    println("OrgTree calculated dimensions - width: $calculatedWidth, height: $calculatedHeight")
    
    // 使用一个外层Box来确保内容可见
    Box(
        modifier = Modifier
            .size(calculatedWidth, calculatedHeight)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize() // 填充父容器
                .verticalScroll(rememberScrollState())
                .horizontalScroll(rememberScrollState())
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    translationX = offsetX,
                    translationY = offsetY
                )
                .pointerInput(Unit) {
                    detectTransformGestures { centroid, pan, zoom, rotation ->
                        onTransformGesture(centroid, pan, zoom, rotation)
                    }
                }
        ) {
            PhcVerticalOrgTree(dataSource, true, stationViewModel, selectedOptimizerOption)
        }
    }
}

data class DeviceNode<T>(val device: T, val children: List<DeviceNode<T>> = emptyList())

sealed class DeviceNodeData {
    data class InverterNode(
        val inverter: Inverter?=null,
    ) : DeviceNodeData()
    data class CollectorNode(
        val collector: StationView.InnerCollector?=null,
    ) : DeviceNodeData()

    data class GroupNode(
        val group: StationView.InnerGroup?=null,
    ) : DeviceNodeData()

    data class OptimizerNode(
        val optimizer: StationView.InnerOptimizer?=null
    ) : DeviceNodeData()
}


@Composable
private fun NoChildrenNodesRow(
    nodes: List<DeviceNode<DeviceNodeData>>,
    layoutDirection: LayoutDirection,
    spacing: Dp,
    lineColor: Color,
    isTopLevel: Boolean,
    stationViewModel: StationViewModel,
    selectedOptimizerOption: String,
    onPositioned: (coordinates: Pair<Float, Float>) -> Unit

) {
    if (nodes.isNotEmpty()) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(spacing),
            modifier = Modifier
                .onGloballyPositioned { coordinates ->
                    onPositioned(
                        Pair(
                            coordinates.positionInParent().y,
                            coordinates.size.height.toFloat()
                        )
                    )
                }
                .drawNodesConnectionLine(lineColor, layoutDirection)
        ) {
            val boxModifier = Modifier
                .drawHorizontalTreeLine(
                    isTopLevel,
                    lineColor,
                    spacing,
                    false,
                    hasChildren = false
                )

            var selectedComponent by remember { mutableStateOf<StationView.InnerOptimizer?>(null) }
            var showTurnOffDialog by remember { mutableStateOf(false) }

            nodes.forEach { item ->
                  if(item.device is DeviceNodeData.OptimizerNode)
                  {
                      val isSelected = selectedComponent?.chipId == item.device.optimizer!!.chipId

                      val displayValue = calculateDisplayValue(
                          optimizer = item.device.optimizer,
                          selectedOptimizerOption = selectedOptimizerOption
                      )

                      SolarPanelItem(
                          power = if (item.device.optimizer.status == 2) ""
                                  else if (item.device.optimizer.power != -1f) "${item.device.optimizer!!.power}W"
                                  else "",
                          voltage = if (item.device.optimizer.status == 2) stringResource(Res.string.status_closed)
                                    else displayValue,
                          id = if(item.device.optimizer.chipId.length>6) item.device.optimizer!!.chipId.substring(2,7)
                               else item.device.optimizer.chipId,
                          backgroundImage = when (item.device.optimizer.status) {
                              1 -> {
                                  // 使用额定功率比例来判断
                                  val powerPercent = item.device.optimizer.ratedPowerRatio

                                  when {
                                      powerPercent > 80f -> painterResource(Res.drawable.station_view_normal_100)
                                      powerPercent > 60f -> painterResource(Res.drawable.station_view_normal_80)
                                      powerPercent > 40f -> painterResource(Res.drawable.station_view_normal_60)
                                      powerPercent > 20f -> painterResource(Res.drawable.station_view_normal_40)
                                      else -> painterResource(Res.drawable.station_view_normal_20)
                                  }
                              }
                              2 -> painterResource(Res.drawable.station_view_offline)
                              3 -> painterResource(Res.drawable.station_view_abnormal)
                              else -> painterResource(Res.drawable.station_view_normal_60)
                          },
                          modifier = boxModifier.size(60.dp, 90.dp).clickable {
                              selectedComponent = item.device.optimizer
                          }.then(
                              if (isSelected) {
                                  Modifier.drawBehind {
                                      drawRect(Color.Yellow.copy(alpha = 0.3f))
                                  }
                              } else {
                                  Modifier
                              }
                          )
                      )
                  }
            }

            val selectedComponentLocal = selectedComponent
            if (selectedComponentLocal != null) {
                val statusText = when(selectedComponentLocal.status) {
                    1 -> stringResource(Res.string.status_normal)
                    2 -> stringResource(Res.string.status_closed)
                    3 -> stringResource(Res.string.status_warning)
                    else -> stringResource(Res.string.status_normal)
                }
                
                val powerText = if (selectedComponentLocal.power == -1f) "" else "${selectedComponentLocal.power} W"
                val voltageText = if (selectedComponentLocal.outputVoltage == -1) "" else "${(selectedComponentLocal.outputVoltage/1000).roundTo2Decimals()} V"
                val temperatureText = if (selectedComponentLocal.componentTemperature == -1) "" else "${selectedComponentLocal.componentTemperature} °C"
                
                // 查找当前组串下的所有组件
                val groupId = selectedComponentLocal.groupId
                val componentsInSameGroup = nodes.filter { 
                    (it.device as? DeviceNodeData.OptimizerNode)?.optimizer?.groupId == groupId 
                }.mapNotNull { (it.device as? DeviceNodeData.OptimizerNode)?.optimizer }
                
                // 判断组串中所有组件是否状态一致
                val hasNormal = componentsInSameGroup.any { it.status == 1 }
                val hasWarning = componentsInSameGroup.any { it.status == 3 }
                val hasClosed = componentsInSameGroup.any { it.status == 2 }
                val allSameStatus = !((hasNormal && hasClosed) || (hasWarning && hasClosed))
                
                // 获取组串状态 - 如果所有组件状态一致，则取第一个组件的状态
                val groupStatus = if (allSameStatus) componentsInSameGroup.firstOrNull()?.status ?: -1 else -1
                
                var showGroupTurnOffDialog by remember { mutableStateOf(false) }
                
                DeviceDetailDialog(
                    deviceNo = if(selectedComponentLocal.chipId.length > 6) selectedComponentLocal.chipId.substring(2,7) else selectedComponentLocal.chipId,
                    deviceState = statusText,
                    devicePower = powerText,
                    outputVoltage = voltageText,
                    deviceTemperature = temperatureText,
                    deviceGroup = selectedComponentLocal.groupName,
                    smartSwitchEnabled = selectedComponentLocal.status != 2,
                    onSmartSwitchChanged = { isEnabled ->
                        if (!isEnabled) {
                            showTurnOffDialog = true
                        }
                    },
                    // 当组串内所有设备状态一致时，显示组串开关
                    showGroupSwitch = allSameStatus && componentsInSameGroup.size > 1,
                    groupSwitchEnabled = groupStatus != 2,
                    onGroupSwitchChanged = {
                        showGroupTurnOffDialog = true
                    },
                    onDismiss = { selectedComponent = null }
                )


                // 组串操作确认对话框
                if (showGroupTurnOffDialog) {
                    val operationFailedStr = stringResource(Res.string.operation_failed)
                    val operationSuccessRefreshLaterStr = stringResource(Res.string.operation_success_refresh_later)
                    val turnOffGroupsTitle = stringResource(Res.string.confirm_turn_off_groups)
                    val turnOnGroupsTitle = stringResource(Res.string.confirm_turn_on_groups)

                    val selectedComponentLocal = selectedComponent
                    if (selectedComponentLocal != null) {
                        val groupId = selectedComponentLocal.groupId
                        val componentsInSameGroup = nodes.filter {
                            (it.device as? DeviceNodeData.OptimizerNode)?.optimizer?.groupId == groupId
                        }.mapNotNull { (it.device as? DeviceNodeData.OptimizerNode)?.optimizer }

                        // 获取组串状态 - 如果所有组件状态一致，则取第一个组件的状态
                        val hasNormal = componentsInSameGroup.any { it.status == 1 }
                        val hasWarning = componentsInSameGroup.any { it.status == 3 }
                        val hasClosed = componentsInSameGroup.any { it.status == 2 }
                        val allSameStatus = !((hasNormal && hasClosed) || (hasWarning && hasClosed))
                        val groupStatus = if (allSameStatus) componentsInSameGroup.firstOrNull()?.status ?: -1 else -1

                        val isGroupOn = groupStatus != 2

                        CustomDialog(
                            confirmStr = if (isGroupOn)
                                turnOffGroupsTitle.replace("%s", selectedComponentLocal.groupName)
                            else
                                turnOnGroupsTitle.replace("%s", selectedComponentLocal.groupName),
                            onConfirm = {
                                // 关闭对话框
                                showGroupTurnOffDialog = false

                                // 传递的操作 1是关闭 2是打开
                                val flag = if (isGroupOn) 1 else 2

                                // 调用组串控制接口，电站id应该传null
                                stationViewModel.remoteControllerAck(
                                    null,
                                    selectedComponentLocal.groupId,
                                    null,
                                    flag,
                                    errorBlock = {
                                        ToastUtil.showLong(operationFailedStr)
                                    }
                                ) {
                                    ToastUtil.showLong(operationSuccessRefreshLaterStr)
                                    selectedComponent = null
                                }
                            },
                            onCancel = {
                                showGroupTurnOffDialog = false
                            }
                        )
                    }
                }


            }

            ShowTurnOffComponentDialog(
                showDialog = showTurnOffDialog,
                selectedComponent = selectedComponent,
                stationViewModel = stationViewModel,
                onDismiss = { showTurnOffDialog = false },
                onSuccess = { selectedComponent = null }
            )
        }
    }
}

@Composable
fun PhcVerticalOrgTree(dataSource: List<DeviceNode<DeviceNodeData>>, isTopLevel: Boolean = true,
                       stationViewModel: StationViewModel,
                       selectedOptimizerOption: String) {
    val spacing = 20.dp
    val lineColor = MaterialTheme.orgTreeColorScheme.lineColor
    val layoutCoordinates = remember { mutableMapOf<Int, Pair<Float, Float>>() }
    val noChildrenCoordinates = remember { mutableMapOf<Int, Pair<Float, Float>>() }

    // 将节点分为有子节点和无子节点两组
    val (nodesWithChildren, nodesWithoutChildren) = dataSource.partition { it.children.isNotEmpty() }
    
    // 将无子节点分成两行
    val halfSize = nodesWithoutChildren.size / 2
    val firstRow = nodesWithoutChildren.take(halfSize)
    val secondRow = nodesWithoutChildren.drop(halfSize)

    Column(
        modifier = Modifier
            .onGloballyPositioned { 
                layoutCoordinates.clear()
                noChildrenCoordinates.clear()
            }
            .drawVerticalTreeLine(dataSource, layoutCoordinates, lineColor, spacing)
            .drawNoChildrenVerticalLine(noChildrenCoordinates, lineColor)
    ) {
        // 渲染有子节点的节点
        nodesWithChildren.forEachIndexed { index, item ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.onGloballyPositioned { coordinates ->
                    layoutCoordinates[index] = Pair(
                        coordinates.positionInParent().y,
                        coordinates.size.height.toFloat()
                    )
                }
            ) {
                NodeWithChildren(
                    item = item,
                    expended = true,
                    isTopLevel = isTopLevel,
                    lineColor = lineColor,
                    spacing = spacing
                )
                
                // 判断是否只有一个子节点
                if (item.children.size == 1) {
                    // 单个子节点，直接在同一行显示
                    Spacer(modifier = Modifier.width(spacing * 2))
                    NodeWithChildren(
                        item = item.children[0],
                        expended = true,
                        isTopLevel = false,
                        lineColor = lineColor,
                        spacing = spacing
                    )
                    // 如果子节点还有子节点，递归显示
                    if (item.children[0].children.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(spacing * 2))
                        // 单个子节点，直接在同一行显示
                        if(item.children[0].children.size==1)
                        {
                            NodeWithChildren(
                                item = item.children[0].children[0],
                                expended = true,
                                isTopLevel = false,
                                lineColor = lineColor,
                                spacing = spacing
                            )
                            Spacer(modifier = Modifier.width(spacing * 2))
                            PhcVerticalOrgTree(item.children[0].children[0].children, false,stationViewModel,selectedOptimizerOption)
                        }
                        else
                        {
                            PhcVerticalOrgTree(item.children[0].children, false,stationViewModel,selectedOptimizerOption)
                        }
                    }
                } else {
                    // 多个子节点，保持原有的垂直布局
                    Spacer(modifier = Modifier.width(spacing * 2))
                    PhcVerticalOrgTree(item.children, false,stationViewModel,selectedOptimizerOption)
                }
            }
            Spacer(modifier = Modifier.height(spacing))
        }

        // 渲染无子节点的第一行
        NoChildrenNodesRow(
            nodes = firstRow,
            layoutDirection = LayoutDirection.Rtl,
            spacing = spacing,
            lineColor = lineColor,
            isTopLevel = isTopLevel,
            stationViewModel,
            selectedOptimizerOption,
            onPositioned = { coordinates ->
                noChildrenCoordinates[0] = coordinates
            }
        )
        if (firstRow.isNotEmpty()) {
            Spacer(modifier = Modifier.height(spacing))
        }

        // 渲染无子节点的第二行
        NoChildrenNodesRow(
            nodes = secondRow,
            layoutDirection = LayoutDirection.Ltr,
            spacing = spacing,
            lineColor = lineColor,
            isTopLevel = isTopLevel,
            stationViewModel,
            selectedOptimizerOption,
            onPositioned = { coordinates ->
                noChildrenCoordinates[1] = coordinates
            }
        )
    }
}



@Composable
private fun NodeWithChildren(
    item: DeviceNode<DeviceNodeData>,
    expended: Boolean,
    isTopLevel: Boolean,
    lineColor: Color,
    spacing: Dp
) {
    val boxModifier = Modifier
        .drawHorizontalTreeLine(
            isTopLevel,
            lineColor,
            spacing,
            expended,
            hasChildren = true
        )
        .clip(RoundedCornerShape(2.dp))

    DeviceBox(boxModifier = boxModifier, deviceData = item.device)
}

@Composable
private fun DeviceBox(boxModifier: Modifier, deviceData: DeviceNodeData) {
    var showDeviceInfoDialog by remember { mutableStateOf(false) }
    var isPressed by remember { mutableStateOf(false) }
    
    Box(
        modifier = boxModifier
            .noRippleClickable { 
                when (deviceData) {
                    is DeviceNodeData.InverterNode -> {
                        if (deviceData.inverter != null) {
                            isPressed = true
                            showDeviceInfoDialog = true
                        }
                    }
                    is DeviceNodeData.CollectorNode -> {
                        if (deviceData.collector != null) {
                            isPressed = true
                            showDeviceInfoDialog = true
                        }
                    }
                    is DeviceNodeData.GroupNode -> {
                        if (deviceData.group != null) {
                            isPressed = true
                            showDeviceInfoDialog = true
                        }
                    }
                    else -> {}
                }
            },
        contentAlignment = Alignment.Center
    ) {
        when (deviceData) {
            is DeviceNodeData.InverterNode -> {
                Image(
                    painter = painterResource( Res.drawable.station_view_inverter),
                    contentDescription = "Inverter Background",
                    modifier = Modifier.size(70.dp, 80.dp)
                )
            }
            is DeviceNodeData.CollectorNode -> {
                Image(
                    painter = painterResource( Res.drawable.collector),
                    contentDescription = "Collector Background",
                    modifier = Modifier.size(60.dp, 80.dp)

                )
            }
            is DeviceNodeData.GroupNode -> {
                Image(
                    painter = painterResource( Res.drawable.group),
                    contentDescription = "Group Background",
                    modifier = Modifier.size(80.dp, 80.dp)
                )
            }
            else -> {}
        }
        
        if (isPressed) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(Color.Yellow.copy(alpha = 0.3f))
            )
        }
    }

    // 显示设备信息对话框
    when (deviceData) {
        is DeviceNodeData.InverterNode -> {
            if (deviceData.inverter != null) {
                DeviceInfoDialog(
                    showDialog = showDeviceInfoDialog,
                    onDismiss = { 
                        showDeviceInfoDialog = false
                        isPressed = false
                    },
                    deviceData = deviceData
                )
            }
        }
        is DeviceNodeData.CollectorNode -> {
            if (deviceData.collector != null) {
                DeviceInfoDialog(
                    showDialog = showDeviceInfoDialog,
                    onDismiss = { 
                        showDeviceInfoDialog = false
                        isPressed = false
                    },
                    deviceData = deviceData
                )
            }
        }
        is DeviceNodeData.GroupNode -> {
            if (deviceData.group != null) {
                DeviceInfoDialog(
                    showDialog = showDeviceInfoDialog,
                    onDismiss = { 
                        showDeviceInfoDialog = false
                        isPressed = false
                    },
                    deviceData = deviceData
                )
            }
        }
        else -> {}
    }
}

private fun Modifier.drawNodesConnectionLine(
    lineColor: Color,
    layoutDirection: LayoutDirection
) = this.drawBehind {
    // 绘制连接所有节点的水平线
    if (size.width > 0) {
        drawLine(
            color = lineColor,
            start = Offset(0f, size.height / 2),
            end = Offset(size.width, size.height / 2),
            strokeWidth = 0.5.dp.toPx()
        )
    }
}




private fun Modifier.drawHorizontalTreeLine(
    isTopLevel: Boolean,
    lineColor: Color,
    space: Dp,
    expended: Boolean,
    hasChildren: Boolean
) = this.drawBehind {
    // 绘制左侧连接线
    if (!isTopLevel) {
        drawLine(
            color = lineColor,
            start = Offset(-space.toPx(), size.height / 2),
            end = Offset(0f, size.height / 2),
            strokeWidth = 0.5.dp.toPx()
        )
    }
    // 绘制右侧连接线
    if (expended && hasChildren) {
        drawLine(
            color = lineColor,
            start = Offset(size.width, size.height / 2),
            end = Offset(size.width + space.toPx(), size.height / 2),
            strokeWidth = 0.5.dp.toPx()
        )
    }
}

private fun Modifier.drawVerticalTreeLine(
    dataSource: List<DeviceNode<DeviceNodeData>>,
    layoutCoordinates: Map<Int, Pair<Float, Float>>,
    lineColor: Color,
    space: Dp
) = this.drawBehind {
    // 获取所有节点的坐标（只包括有子节点的节点）
    val allCoordinates = mutableListOf<Pair<Float, Float>>()
    
    // 只添加有子节点的节点坐标
    if (layoutCoordinates.isNotEmpty()) {
        layoutCoordinates.values.forEach { coordinate ->
            allCoordinates.add(coordinate)
        }
    }
    
    // 确保至少有两个节点才绘制垂直线
    if (allCoordinates.size > 1) {
        // 绘制连接所有节点的垂直线，只连接到最后一个有子节点
        drawLine(
            color = lineColor,
            start = Offset(
                -space.toPx(),
                allCoordinates.first().first + (allCoordinates.first().second / 2)
            ),
            end = Offset(
                -space.toPx(),
                allCoordinates.last().first + (allCoordinates.last().second / 2)
            ),
            strokeWidth = 0.5.dp.toPx()
        )
    }
}

private fun Modifier.drawNoChildrenVerticalLine(
    layoutCoordinates: Map<Int, Pair<Float, Float>>,
    lineColor: Color
) = this.drawBehind {
    if (layoutCoordinates.size > 1) {
        layoutCoordinates[0]?.let { first ->
            layoutCoordinates[1]?.let { second ->
                drawLine(
                    color = lineColor,
                    start = Offset(
                        -20.dp.toPx(),
                        first.first + (first.second / 2)
                    ),
                    end = Offset(
                        -20.dp.toPx(),
                        second.first + (second.second / 2)
                    ),
                    strokeWidth = 0.5.dp.toPx()
                )
            }
        }
    }
}

private data class OrgTreeColors(
    val lineColor: Color,
    val borderColor: Color
)

private val MaterialTheme.orgTreeColorScheme: OrgTreeColors
    @Composable
    get() = OrgTreeColors(
        lineColor = if (isSystemInDarkTheme()) {
            colorScheme.outline
        } else {
            Color.Black
        },
        borderColor = if (isSystemInDarkTheme()) {
            colorScheme.outline
        } else {
            colorScheme.onPrimary
        }
    )

@Composable
private fun Modifier.noRippleClickable(onClick: () -> Unit): Modifier = clickable(
    interactionSource = remember { MutableInteractionSource() },
    indication = null,
    onClick = onClick
)