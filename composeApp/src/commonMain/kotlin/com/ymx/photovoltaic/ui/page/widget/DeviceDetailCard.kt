package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.component_status
import photovoltaic_kmp_app.composeapp.generated.resources.component_temperature
import photovoltaic_kmp_app.composeapp.generated.resources.group_belong
import photovoltaic_kmp_app.composeapp.generated.resources.group_shutdown_switch
import photovoltaic_kmp_app.composeapp.generated.resources.home_error
import photovoltaic_kmp_app.composeapp.generated.resources.home_offline
import photovoltaic_kmp_app.composeapp.generated.resources.home_ok
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_shutdown_switch
import photovoltaic_kmp_app.composeapp.generated.resources.output_power
import photovoltaic_kmp_app.composeapp.generated.resources.output_voltage
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_close
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_info
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_off
import photovoltaic_kmp_app.composeapp.generated.resources.status_warning

@Composable
fun DeviceDetailDialog(
    deviceNo: String,
    deviceState: String,
    devicePower: String,
    outputVoltage: String,
    deviceTemperature: String,
    deviceGroup: String,
    smartSwitchEnabled: Boolean = false,
    onSmartSwitchChanged: (Boolean) -> Unit = {},
    showGroupSwitch: Boolean = false,  // 是否显示组串开关
    groupSwitchEnabled: Boolean = false,  // 组串开关状态
    onGroupSwitchChanged: (Boolean) -> Unit = {},  // 组串开关回调
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false // 允许对话框使用全屏宽度
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize() // 使对话框内容填充整个屏幕
                .background(Color.Transparent), // 背景透明
            contentAlignment = Alignment.BottomCenter // 内容对齐到底部
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth() // 使卡片宽度充满屏幕
                    .wrapContentHeight() // 高度根据内容自适应
                , // 只保留垂直方向的边距
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp), // 只有顶部圆角
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp) // 添加一些阴影
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp) // 增加卡片内部填充
                ) {
                    // 关闭按钮 - 添加灰色圆形背景
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(8.dp)
                            .size(30.dp) // 稍微增大点击区域
                            .clip(CircleShape)
                            .background(Color.LightGray.copy(alpha = 0.3f)) // 灰色半透明背景
                            .clickable { onDismiss() },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(Res.drawable.station_view_close),
                            contentDescription = "关闭",
                            modifier = Modifier.size(12.dp) // 缩小图标大小
                        )
                    }

                    // 内容
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 20.dp, bottom = 16.dp, start = 20.dp, end = 20.dp), // 增加左右内边距
                        horizontalAlignment = Alignment.CenterHorizontally // 内容水平居中
                    ) {
                        // 设备编号行
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(28.dp) // 减小尺寸
                                    .clip(CircleShape)
                                    .background(Color(0xFFFBE9E7)),
                                contentAlignment = Alignment.Center
                            ) {
                                Image(
                                    painter = painterResource(Res.drawable.station_view_optimizer),
                                    contentDescription = "设备图标",
                                    modifier = Modifier.size(16.dp) // 减小图标尺寸
                                )
                            }
                            Spacer(modifier = Modifier.width(6.dp)) // 减少间距
                            Text(
                                text = deviceNo,
                                style = TextStyle(
                                    fontSize = 16.sp, // 减小字体
                                    fontWeight = FontWeight.Bold
                                )
                            )
                        }

                        Spacer(modifier = Modifier.height(6.dp)) // 减少间距

                        // 设备状态
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.component_status),
                                style = TextStyle(fontSize = 14.sp)
                            )
                            
                            // 状态图标和文字连在一起显示
                            val (iconRes, textColor, bgColor) = when (deviceState) {
                                stringResource(Res.string.status_normal) -> Triple(
                                    Res.drawable.home_ok,
                                    Color(0xFF4CAF50),
                                    Color(0xFFE8F5E9)
                                )
                                stringResource(Res.string.status_warning) -> Triple(
                                    Res.drawable.home_error,
                                    Color(0xFFF44336),
                                    Color(0xFFFBE9E7)
                                )
                                stringResource(Res.string.status_off) -> Triple(
                                    Res.drawable.home_offline,
                                    Color(0xFF9E9E9E),
                                    Color(0xFFF5F5F5)
                                )
                                else -> Triple(
                                    Res.drawable.home_ok,
                                    Color(0xFF4CAF50),
                                    Color(0xFFE8F5E9)
                                )
                            }
                            
                            Box(
                                modifier = Modifier
                                    .clip(RoundedCornerShape(16.dp))
                                    .background(bgColor)
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    Image(
                                        painter = painterResource(iconRes),
                                        contentDescription = deviceState,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    
                                    Text(
                                        text = deviceState,
                                        style = TextStyle(fontSize = 13.sp, color = textColor) // 减小字体大小
                                    )
                                }
                            }
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 设备功率
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.output_power),
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                            Text(
                                text = devicePower,
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 输出电压
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.output_voltage),
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                            Text(
                                text = outputVoltage,
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 组件温度
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.component_temperature),
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                            Text(
                                text = deviceTemperature,
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 所属组串
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.group_belong),
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                            Text(
                                text = deviceGroup,
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 优化器开关 (改为优化器关断开关)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 3.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(Res.string.optimizer_shutdown_switch),
                                style = TextStyle(fontSize = 14.sp) // 减小字体大小
                            )
                            
                            Switch(
                                checked = smartSwitchEnabled,
                                onCheckedChange = onSmartSwitchChanged,
                                modifier = Modifier.size(width = 45.dp, height = 25.dp), // 调小开关大小
                                colors = SwitchDefaults.colors(
                                    checkedThumbColor = Color.White,
                                    checkedTrackColor = Color(73, 176, 45, 255),
                                    uncheckedThumbColor = Color.White,
                                    uncheckedTrackColor = Color.Gray.copy(alpha = 0.5f),
                                    // 移除边框
                                    checkedBorderColor = Color.Transparent,
                                    uncheckedBorderColor = Color.Transparent
                                )
                            )
                        }
                        
                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth(0.9f) // 减少宽度
                                .padding(vertical = 6.dp),
                            thickness = 1.dp,
                            color = Color.LightGray.copy(alpha = 0.5f)
                        )
                        
                        // 组串开关 - 只在所有组件状态一致时显示 (改为组串关断开关)
                        if (showGroupSwitch) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth(0.9f) // 减少宽度
                                    .padding(vertical = 3.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(Res.string.group_shutdown_switch),
                                    style = TextStyle(fontSize = 14.sp) // 减小字体大小
                                )
                                
                                Switch(
                                    checked = groupSwitchEnabled,
                                    onCheckedChange = onGroupSwitchChanged,
                                    modifier = Modifier.size(width = 45.dp, height = 25.dp),
                                    colors = SwitchDefaults.colors(
                                        checkedThumbColor = Color.White,
                                        checkedTrackColor = Color(73, 176, 45, 255),
                                        uncheckedThumbColor = Color.White,
                                        uncheckedTrackColor = Color.Gray.copy(alpha = 0.5f),
                                        checkedBorderColor = Color.Transparent,
                                        uncheckedBorderColor = Color.Transparent
                                    )
                                )
                            }
                            
                            HorizontalDivider(
                                modifier = Modifier
                                    .fillMaxWidth(0.9f) // 减少宽度
                                    .padding(vertical = 6.dp),
                                thickness = 1.dp,
                                color = Color.LightGray.copy(alpha = 0.5f)
                            )
                        }
                        
                        // 底部图片 - 缩小底部图片尺寸和宽度
                        Image(
                            painter = painterResource(Res.drawable.station_view_info),
                            contentDescription = "信息图",
                            modifier = Modifier
                                .fillMaxWidth(0.6f) // 减少图片宽度，只占80%的宽度
                                .height(120.dp) // 进一步缩小图片高度
                                .padding(vertical = 6.dp), // 减少垂直内边距
                            contentScale = ContentScale.FillWidth // 调整为填充宽度
                        )
                    }
                }
            }
        }
    }
}