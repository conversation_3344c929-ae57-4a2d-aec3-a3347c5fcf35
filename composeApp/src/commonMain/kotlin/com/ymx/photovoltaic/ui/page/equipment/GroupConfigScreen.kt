package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.DeviceTwoTextRow
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.belongs_to_station
import photovoltaic_kmp_app.composeapp.generated.resources.creation_date
import photovoltaic_kmp_app.composeapp.generated.resources.existing_optimizers
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.group_config
import photovoltaic_kmp_app.composeapp.generated.resources.input_group_name_search

/**
 * 组串配置页面
 */
@Composable
fun GroupConfigScreen(
    navHostController: NavHostController,
    groupList: List<Group>,
    stationName: String,
    collectorId: String = ""
) {

    var searchQuery by remember { mutableStateOf("") } // 用于存储搜索框中的输入

    val filteredItems = groupList.filter {
        it.groupName.contains(searchQuery, ignoreCase = true)
        // 只过滤名字字段
    }

    Scaffold(
        topBar = {
            TopBar(
                textStr = stringResource(Res.string.group_config),
                backClick = { navHostController.popBackStack() },
            )
        },
        containerColor = Grey_F5,
        floatingActionButton = {
            FloatingButton(
                onAddClicked = {
                    navHostController.navigate("${Route.GROUP_NEW}/$collectorId")
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 0.dp)
        ) {

            CommonSearch(
                searchQuery, 
                onQueryChange = { searchQuery = it }, 
                placeholderTextResId = Res.string.input_group_name_search,
                modifier = Modifier
                    .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 10.dp)
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(25.dp))
            )

            LazyColumn {
                items(filteredItems) { group ->
                    DeviceItem(
                        imagePainter = painterResource(Res.drawable.group),
                        secondRowText = stringResource(Res.string.belongs_to_station).replace("{station}", stationName),
                        firstRow = {
                            DeviceTwoTextRow(
                                firstText = " ${group.groupName}",
                                secondText = stringResource(Res.string.existing_optimizers).replace("{count}", group.groupNum.toString()),
                                isTextBold = true
                            )
                        },
                        lastRow = {
                            DeviceTextButtonRow(
                                textStr = stringResource(Res.string.creation_date).replace(
                                    "{date}", 
                                    group.createTimeCh.substring(startIndex = 0, endIndex = 10)
                                ),
                                buttonText = stringResource(Res.string.add_optimizer),
                                onButtonClick = {
                                    navHostController.navigate(Route.SCAN+ "/${group.id}")
                                },
                                isTextBold = false
                            )
                        },
                    )
                }
            }
        }
    }
}
