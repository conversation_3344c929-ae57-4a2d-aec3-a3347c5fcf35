package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.data.bean.ScanDevice
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.bean.WarningStats
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class EquipmentViewModel: BaseViewModel<String>() {

    private val _collectorListFlow = MutableStateFlow<List<Collector>>(emptyList())
    val collectorListFlow: StateFlow<List<Collector>> = _collectorListFlow

    private val _groupListFlow = MutableStateFlow<List<Group>>(emptyList())
    val groupListFlow: StateFlow<List<Group>> = _groupListFlow

    private val _optimizerListFlow = MutableStateFlow<List<Optimizer>>(emptyList())
    val optimizerListFlow: StateFlow<List<Optimizer>> = _optimizerListFlow

    private val _stationListFlow = MutableStateFlow<List<Station>>(emptyList())
    val stationListFlow: StateFlow<List<Station>> = _stationListFlow

    private val _scanDeviceFlow = MutableStateFlow<ScanDevice?>(null)
    val scanDeviceFlow: StateFlow<ScanDevice?> = _scanDeviceFlow

    private val _relayListFlow = MutableStateFlow<List<Relay>>(emptyList())
    val relayListFlow: StateFlow<List<Relay>> = _relayListFlow

    private val _relayComponentListFlow = MutableStateFlow<List<Optimizer>>(emptyList())
    val relayComponentListFlow: StateFlow<List<Optimizer>> = _relayComponentListFlow

    private val _relayGroupListFlow = MutableStateFlow<List<Group>>(emptyList())
    val relayGroupListFlow: StateFlow<List<Group>> = _relayGroupListFlow

    private val _relayChipIdMapFlow = MutableStateFlow<Map<String, List<String>>>(emptyMap())
    val relayChipIdMapFlow: StateFlow<Map<String, List<String>>> = _relayChipIdMapFlow
    
    private val _warningTypeCountFlow = MutableStateFlow<WarningStats?>(null)
    val warningTypeCountFlow: StateFlow<WarningStats?> = _warningTypeCountFlow

    /** 请求采集器列表 */
    fun fetchCollectorList(powerStationId: String) {
        launch({
            handleRequest(DataRepository.queryCollectorList(powerStationId)) {
                _collectorListFlow.value = it.reModel.data
            }
        })
    }

    /** 请求组串列表 */
    fun fetchGroupList(cloudId: String) {
        launch({
            handleRequest(DataRepository.queryGroupList(cloudId)) {
                _groupListFlow.value = it.reModel.data
            }
        })
    }

    /** 清空组串列表 */
    fun clearGroupList() {
        _groupListFlow.value = emptyList()
    }

    /** 请求优化器列表 */
    fun fetchOptimizerList(
        powerStationId: String?,
        belongsGroupId: String?,
        belongsGroupFlag: String?,
        pageSize: Int
    ) {
        launch({
            handleRequest(
                DataRepository.queryOptimizerList(powerStationId, belongsGroupId, belongsGroupFlag, pageSize)
            ) {
                _optimizerListFlow.value = it.reModel.data
            }
        })
    }

    /** 新增电站 */
    fun addStation(stationMap: Map<String, String>,
                          errorBlock: () -> Unit = {},
                          successCall: () -> Unit = {}) {
        launch({
            handleRequestWithNull(
                DataRepository.savePowerStationModel(stationMap),
                errorBlock = {
                    errorBlock()
                    true
                }
            )
            {
                successCall.invoke()
            }
        })
    }

    /** 新增或修改优化器 */
    fun addOrModOptimizer(optimizerMap: Map<String, String>,
                          errorBlock: () -> Unit = {},
                           successCall: () -> Unit = {}) {
        launch({
            handleRequestWithNull(
                DataRepository.addOrModOptimizer(optimizerMap),
                errorBlock = {
                    errorBlock()
                    true
                }
            )
            {
                successCall.invoke()
            }
        })
    }

    /** 新增或修改组串 */
    fun addOrModGroup(
        groupMap: Map<String, Any>,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}) {
        launch({
            handleRequestWithNull(
                DataRepository.addOrModGroup(groupMap)
            )
            {
                successCall.invoke()
            }
        })
    }

    /** 新增或修改采集器 */
    fun addOrModCollector(collectorMap: Map<String, String>,
                          errorBlock: () -> Unit = {},
                      successCall: () -> Unit = {}) {
        launch({
            handleRequestWithNull(
                DataRepository.addOrModCollector(collectorMap)
            )
            {
                successCall.invoke()
            }
        })
    }

    fun fetchStationList( mId: String,
                          userType: String) {
        launch({
            handleRequest(
                DataRepository.queryPowerStationList(mId,userType)
            )
            {
                _stationListFlow.value = it.reModel.data
            }
        })
    }

    /** 请求中继列表 */
    fun fetchRelayList(createUserId: String, operationFlag: String, powerId: String) {
        launch({
            handleRequest(DataRepository.queryRelayList(createUserId, operationFlag, powerId)) {
                _relayListFlow.value = it.reModel.data
            }
        })
    }

    /** 保存或更新中继 */
    fun saveOrUpdateRelay(
        relayId: String,
        relayName: String,
        imei: String,
        powerStationId: String,
        createUserId: String,
        id: Int? = null,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.saveOrUpdateRelay(relayId, relayName, imei, powerStationId,createUserId,id),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }

    /** 扫描二维码获取设备信息 */
    fun fetchScanDeviceInfo(
        code: String,
        language: String? = null,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequest(
                DataRepository.getScanDeviceInfo(code, language),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                _scanDeviceFlow.value = it.reModel
                successCall.invoke()
            }
        })
    }

    /** 请求中继下的组件列表 */
    fun fetchRelayComponentList(
        relayId: String,
        pageNo: Int = 1,
        pageSize: Int = 500,
        operationFlag: String? = null,
        chipId: String? = null,
        groupName: String? = null,
        createUserId: String
    ) {
        launch({
            handleRequest(
                DataRepository.queryRelayComponentList(
                    relayId, pageNo, pageSize, operationFlag, chipId, groupName, createUserId
                )
            ) {
                _relayComponentListFlow.value = it.reModel.data
            }
        })
    }

    /** 根据采集器ID请求中继列表 */
    fun fetchRelayListByCloudId(createUserId: String, operationFlag: String, cloudId: String) {
        launch({
            handleRequest(
                DataRepository.queryRelayListForCloudId(createUserId, operationFlag, cloudId)
            ) {
                _relayListFlow.value = it.reModel.data
            }
        })
    }

    /** 修改组件所属中继组 */
    fun changeComponentGroup(
        id: String,
        relayId: String?,
        createUserId: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.changeComponentGroup(id, relayId, createUserId),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }

    /** 修改组件所属中继 */
    fun changeComponent(
        id: String,
        relayId: String?,
        createUserId: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.changeComponent(id, relayId, createUserId),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }

    /** 请求中继下的组串列表 */
    fun fetchRelayGroupList(
        relayId: String,
        groupName: String,
        operationFlag: String,
        createUserId: String,
        pageSize: Int,
    ) {
        launch({
            handleRequest(
                DataRepository.queryRelayGroupList(
                    relayId, groupName, operationFlag, createUserId, pageSize
                )
            ) {
                _relayGroupListFlow.value = it.reModel.data
            }
        })
    }

    /** 获取所有中继器的优化器数据 */
    fun fetchAllRelayComponentData(
        cloudId: String,
        createUserId: String,
        operationFlag: String = "view",
        onComplete: () -> Unit = {}
    ) {
        launch({
            handleRequest(
                DataRepository.queryRelayListForCloudId(createUserId, operationFlag, cloudId)
            ) { relayResponse ->
                val relayList = relayResponse.reModel.data
                _relayListFlow.value = relayList
                
                if (relayList.isEmpty()) {
                    onComplete()
                    return@handleRequest
                }
                
                val relayChipIdMap = mutableMapOf<String, List<String>>()
                var completedCount = 0
                
                relayList.forEach { relay ->
                    handleRequest(
                        DataRepository.queryRelayComponentList(
                            relay.relayId, 1, 500, operationFlag, null, null, createUserId
                        )
                    ) { optimizerResponse ->
                        val optimizers = optimizerResponse.reModel.data
                        val chipIdList = optimizers.mapNotNull { optimizer ->
                            optimizer.chipId.takeIf { it.isNotEmpty() }
                        }
                        
                        relayChipIdMap[relay.relayId] = chipIdList
                        
                        completedCount++
                        
                        if (completedCount == relayList.size) {
                            _relayChipIdMapFlow.value = relayChipIdMap
                            onComplete()
                        }
                    }
                }
            }
        })
    }

    /** 查询警告类型数量 */
    fun fetchWarningTypeCount(powerIdList: List<String>) {
        launch({
            handleRequest(DataRepository.queryWarningTypeCount(powerIdList)) {
                _warningTypeCountFlow.value = it.reModel
            }
        })
    }

}