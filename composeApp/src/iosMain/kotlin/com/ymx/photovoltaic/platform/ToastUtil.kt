package com.ymx.photovoltaic.platform

import kotlinx.cinterop.ExperimentalForeignApi
import platform.UIKit.UIAlertController
import platform.UIKit.UIAlertControllerStyleAlert
import platform.UIKit.UIApplication
import platform.darwin.DISPATCH_TIME_NOW
import platform.darwin.dispatch_after
import platform.darwin.dispatch_get_main_queue
import platform.darwin.dispatch_time

/**
 * Toast封装工具类 - iOS实现
 */
actual object ToastUtil {
    // 定义不同持续时间
    private const val DURATION_SHORT = 2.0 // 2秒
    private const val DURATION_LONG = 3.5  // 3.5秒

    /**
     * 显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShort(message: String) {
        showAlert(message, DURATION_SHORT, false)
    }

    /**
     * 显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLong(message: String) {
        showAlert(message, DURATION_LONG, false)
    }

    /**
     * 在屏幕中央显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShortInCenter(message: String) {
        showAlert(message, DURATION_SHORT, true)
    }

    /**
     * 在屏幕中央显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLongInCenter(message: String) {
        showAlert(message, DURATION_LONG, true)
    }

    /**
     * 显示提示信息
     *
     * @param message 消息内容
     * @param duration 显示时长(秒)
     * @param center 是否居中显示
     */
    @OptIn(ExperimentalForeignApi::class)
    private fun showAlert(message: String, duration: Double, center: Boolean) {
        // 获取当前正在显示的视图控制器
        val rootController = UIApplication.sharedApplication.keyWindow?.rootViewController ?: return
        var currentController = rootController

        // 找到最前面的视图控制器
        while (currentController.presentedViewController != null) {
            currentController = currentController.presentedViewController!!
        }

        // 创建提示控制器
        val alertController = UIAlertController.alertControllerWithTitle(
            null, // 不设置标题
            message,
            UIAlertControllerStyleAlert
        )

        // 显示提示框
        currentController.presentViewController(alertController, true, null)

        // 使用GCD延迟执行关闭操作
        val delayInNanoseconds = (duration * 1_000_000_000).toLong() // 1秒 = 10^9纳秒
        val popTime = dispatch_time(DISPATCH_TIME_NOW, delayInNanoseconds)

        dispatch_after(popTime, dispatch_get_main_queue()) {
            alertController.dismissViewControllerAnimated(true, null)
        }
    }
}